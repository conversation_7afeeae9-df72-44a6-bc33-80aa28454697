﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  StdAfx.cpp
  ByteEdit.cpp
  Common.cpp
  Custom.cpp
  CustomMethodEdit.cpp
  DOD.cpp
  Eraser.cpp
c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2076): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2076): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2076): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2080): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2080): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2080): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2084): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2084): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2084): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2099): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2099): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2099): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2105): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2105): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2105): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2157): warning C4456: “_ctlState”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2127): note: 参见“_ctlState”的声明
c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2960): warning C4457: “filename”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2937): note: 参见“filename”的声明
c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(3007): warning C4456: “file”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserdll\eraser.cpp(2973): note: 参见“file”的声明
  FAT.cpp
  File.cpp
c:\users\<USER>\desktop\eraser\src\eraserdll\file.cpp(400): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\file.cpp(400): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\file.cpp(400): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  FileLockResolver.cpp
  FillMemoryWith.cpp
  FirstLast2kb.cpp
  FreeSpace.cpp
c:\users\<USER>\desktop\eraser\src\eraserdll\freespace.cpp(559): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\freespace.cpp(559): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\freespace.cpp(559): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  Gutmann.cpp
  NTFS.cpp
c:\users\<USER>\desktop\eraser\src\eraserdll\ntfs.cpp(637): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\ntfs.cpp(637): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserdll\ntfs.cpp(637): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  OptionPages.cpp
  Options.cpp
  OptionsDlg.cpp
  Pass.cpp
  PassEditDlg.cpp
  Random.cpp
  正在编译...
  ReportDialog.cpp
  RND.cpp
  sboxes.cpp
  Schneier7Pass.cpp
  SecManDlg.cpp
  SecurityManager.cpp
  tiger.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1216,5): warning MSB8012: TargetPath(C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Release\EraserDll.dll) 与 Linker 的 OutputFile 属性值(C:\Users\<USER>\Desktop\Eraser\src\Bin\OutR\Eraser.dll)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1218,5): warning MSB8012: TargetName(EraserDll) 与 Linker 的 OutputFile 属性值(Eraser)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
    正在创建库 C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Release\EraserDll.lib 和对象 C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Release\EraserDll.exp
  正在生成代码
  All 1235 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  EraserDll.vcxproj -> C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Release\EraserDll.dll
