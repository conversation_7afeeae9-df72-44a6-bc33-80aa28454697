﻿// Erasext.cpp

#include "stdafx.h"
#include "Erasext.h"

#include "..\EraserDll\eraserdll.h"
#include "..\EraserUI\VisualStyles.h"
#include "ConfirmDialog.h"
#include "WipeProgDlg.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CErasextApp

BEGIN_MESSAGE_MAP(CErasextApp, CWinApp)
    //{{AFX_MSG_MAP(CErasextApp)
        // NOTE - the ClassWizard will add and remove mapping macros here.
        //    DO NOT EDIT what you see in these blocks of generated code!
    //}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CErasextApp construction

CErasextApp::CErasextApp()
{
    _set_se_translator(SeTranslator);
}

/////////////////////////////////////////////////////////////////////////////
// The one and only CErasextApp object

CErasextApp theApp;

/////////////////////////////////////////////////////////////////////////////
// Special entry points required for inproc servers

STDAPI DllGetClassObject(REFCLSID rclsid, REFIID riid, LPVOID* ppv)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    TRACE("DllGetClassObject\n");
    return AfxDllGetClassObject(rclsid, riid, ppv);
}

STDAPI DllCanUnloadNow(void)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    TRACE("DllCanUnloadNow\n");
    return S_FALSE; //AfxDllCanUnloadNow();
}

// by exporting DllRegisterServer, you can use regsvr.exe
STDAPI DllRegisterServer(void)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    TRACE("DllRegisterServer\n");
    COleObjectFactory::UpdateRegistryAll();
    return S_OK;
}

BOOL CErasextApp::InitInstance()
{
    TRACE("CErasextApp::InitInstance\n");
    // Register all OLE server (factories) as running.  This enables the
    // OLE libraries to create objects from other applications.
    COleObjectFactory::RegisterAll();

    eraserInit();
    return CWinApp::InitInstance();
}

int CErasextApp::ExitInstance()
{
    eraserEnd();
	return CWinApp::ExitInstance();
}
