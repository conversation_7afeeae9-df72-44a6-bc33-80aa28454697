<?xml version="1.0" encoding="windows-1251"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="EraserDll"
	ProjectGUID="{31F52DA7-B9F6-452D-823F-E1EFDF283995}"
	RootNamespace="EraserDll"
	Keyword="MFCProj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="Debug/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="_DLL_ERASER"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1035"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(TargetName)Dll.pdb"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="Debug/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="_DLL_ERASER"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1035"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(ProjectName).pdb"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="Release/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;_DLL_ERASER"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(TargetName)Dll.pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="Release/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;_DLL_ERASER"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(ProjectName).pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="1"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="Release/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;_DLL_ERASER;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="unicows.lib netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				LinkIncremental="1"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(TargetName)Dll.pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="Release/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;_DLL_ERASER;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(ProjectName).pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="Release/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;_DLL_ERASER"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="unicows.lib netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(TargetName)Dll.pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="Release/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;_DLL_ERASER"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(ProjectName).pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="Debug/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="_DLL_ERASER"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1035"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="unicows.lib netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(TargetName)Dll.pdb"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="Debug/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="_DLL_ERASER"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1035"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(ProjectName).pdb"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="1"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="Release/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;_DLL_ERASER;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="unicows.lib netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				LinkIncremental="1"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(TargetName)Dll.pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="Release/EraserDll.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;_DLL_ERASER;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\EraserDll.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4251;4275"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="netapi32.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Eraser.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(TargetDir)$(ProjectName).pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="ByteEdit.cpp"
				>
			</File>
			<File
				RelativePath="Common.cpp"
				>
			</File>
			<File
				RelativePath="Custom.cpp"
				>
			</File>
			<File
				RelativePath="CustomMethodEdit.cpp"
				>
			</File>
			<File
				RelativePath="DOD.cpp"
				>
			</File>
			<File
				RelativePath="Eraser.cpp"
				>
			</File>
			<File
				RelativePath="Eraser.rc"
				>
			</File>
			<File
				RelativePath="FAT.cpp"
				>
			</File>
			<File
				RelativePath="File.cpp"
				>
			</File>
			<File
				RelativePath="FileLockResolver.cpp"
				>
			</File>
			<File
				RelativePath="FillMemoryWith.cpp"
				>
			</File>
			<File
				RelativePath="FirstLast2kb.cpp"
				>
			</File>
			<File
				RelativePath="FreeSpace.cpp"
				>
			</File>
			<File
				RelativePath="Gutmann.cpp"
				>
			</File>
			<File
				RelativePath="NTFS.cpp"
				>
			</File>
			<File
				RelativePath="OptionPages.cpp"
				>
			</File>
			<File
				RelativePath="Options.cpp"
				>
			</File>
			<File
				RelativePath="OptionsDlg.cpp"
				>
			</File>
			<File
				RelativePath="Pass.cpp"
				>
			</File>
			<File
				RelativePath="PassEditDlg.cpp"
				>
			</File>
			<File
				RelativePath="Random.cpp"
				>
			</File>
			<File
				RelativePath="ReportDialog.cpp"
				>
			</File>
			<File
				RelativePath="RND.cpp"
				>
			</File>
			<File
				RelativePath="sboxes.cpp"
				>
			</File>
			<File
				RelativePath="Schneier7Pass.cpp"
				>
			</File>
			<File
				RelativePath="SecManDlg.cpp"
				>
			</File>
			<File
				RelativePath="SecurityManager.cpp"
				>
			</File>
			<File
				RelativePath="StdAfx.cpp"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tiger.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="ByteEdit.h"
				>
			</File>
			<File
				RelativePath="Common.h"
				>
			</File>
			<File
				RelativePath="Custom.h"
				>
			</File>
			<File
				RelativePath="CustomMethodEdit.h"
				>
			</File>
			<File
				RelativePath="DOD.h"
				>
			</File>
			<File
				RelativePath="Eraser.h"
				>
			</File>
			<File
				RelativePath="EraserDll.h"
				>
			</File>
			<File
				RelativePath="EraserDllInternal.h"
				>
			</File>
			<File
				RelativePath=".\EraserExport.h"
				>
			</File>
			<File
				RelativePath="FAT.h"
				>
			</File>
			<File
				RelativePath="File.h"
				>
			</File>
			<File
				RelativePath="FileLockResolver.h"
				>
			</File>
			<File
				RelativePath="FillMemoryWith.h"
				>
			</File>
			<File
				RelativePath="FirstLast2kb.h"
				>
			</File>
			<File
				RelativePath="FreeSpace.h"
				>
			</File>
			<File
				RelativePath="Gutmann.h"
				>
			</File>
			<File
				RelativePath="isaac.hpp"
				>
			</File>
			<File
				RelativePath="NTFS.h"
				>
			</File>
			<File
				RelativePath="OptionPages.h"
				>
			</File>
			<File
				RelativePath="options.h"
				>
			</File>
			<File
				RelativePath="OptionsDlg.h"
				>
			</File>
			<File
				RelativePath="Pass.h"
				>
			</File>
			<File
				RelativePath="PassEditDlg.h"
				>
			</File>
			<File
				RelativePath="Random.h"
				>
			</File>
			<File
				RelativePath="ReportDialog.h"
				>
			</File>
			<File
				RelativePath="Resource.h"
				>
			</File>
			<File
				RelativePath="RND.h"
				>
			</File>
			<File
				RelativePath="Schneier7pass.h"
				>
			</File>
			<File
				RelativePath="SecManDlg.h"
				>
			</File>
			<File
				RelativePath="SecurityManager.h"
				>
			</File>
			<File
				RelativePath="Stack.h"
				>
			</File>
			<File
				RelativePath="StdAfx.h"
				>
			</File>
			<File
				RelativePath="tiger.h"
				>
			</File>
			<File
				RelativePath="..\version.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;cnt;rtf;gif;jpg;jpeg;jpe"
			>
			<File
				RelativePath="res\Eraser.ico"
				>
			</File>
			<File
				RelativePath="..\res\Eraser.ico"
				>
			</File>
			<File
				RelativePath="res\Eraser.rc2"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
		<Global
			Name="RESOURCE_FILE"
			Value="Eraser.rc"
		/>
	</Globals>
</VisualStudioProject>
