// key.h
#ifndef KEY_H
#define KEY_H

// registry definitions
#include <winreg.h>
#include <regstr.h>

#define MAX_KEY_LENGTH 256

/////////////////////////////////////////////////////////////////////////////
// CKey

class CKey
{
public:
    CKey();
    virtual ~CKey();

    virtual BOOL SetValue(LPCTSTR lpszValue, LPCTSTR lpszValueName = NULL);
    virtual BOOL SetValue(DWORD, LPCTSTR lpszValueName = NULL);
    virtual BOOL SetValue(BOOL, LPCTSTR lpszValueName = NULL);
    virtual BOOL SetValue(LPVOID, LPCTSTR lpszValueName = NULL, DWORD dwSize = 0);

    virtual BOOL GetValue(CString& str, LPCTSTR lpszValueName = NULL, CString strDefault = _T(""));
    virtual BOOL GetValue(DWORD&, LPCTSTR lpszValueName = NULL, DWORD dwDefault = 0);
    virtual BOOL GetValue(BOOL&, LPCTSTR lpszValueName = NULL, BOOL bDefault = FALSE);
    virtual BOOL GetValue(LPVOID, LPCTSTR lpszValueName = NULL);
	
	virtual BOOL GetNextValueName(CString& strValName, DWORD index = 0, LPDWORD valType = NULL);

    virtual BOOL IsEmpty();
    virtual DWORD GetValueSize(LPCTSTR lpszValueName = NULL);

    virtual BOOL DeleteValue(LPCTSTR lpszValueName);
    static BOOL DeleteKey(HKEY, LPCTSTR);

    virtual BOOL Open(HKEY hKey, LPCTSTR lpszKeyName, BOOL bCreate = TRUE);
	virtual HKEY GetHandle();
    virtual void Close();

protected:
    HKEY m_hKey;
};


class CIniKey : public CKey {
public:
	CIniKey();
	virtual ~CIniKey();

	virtual BOOL SetValue(LPCTSTR lpszValue, LPCTSTR lpszValueName = NULL);
	virtual BOOL SetValue(DWORD, LPCTSTR lpszValueName = NULL);
	virtual BOOL SetValue(BOOL, LPCTSTR lpszValueName = NULL);
	virtual BOOL SetValue(LPVOID, LPCTSTR lpszValueName = NULL, DWORD dwSize = 0);

	virtual BOOL GetValue(CString& str, LPCTSTR lpszValueName = NULL, CString strDefault = _T(""));
	virtual BOOL GetValue(DWORD&, LPCTSTR lpszValueName = NULL, DWORD dwDefault = 0);
	virtual BOOL GetValue(BOOL&, LPCTSTR lpszValueName = NULL, BOOL bDefault = FALSE);
	virtual BOOL GetValue(LPVOID, LPCTSTR lpszValueName = NULL);

	virtual BOOL GetNextValueName(CString& strValName, DWORD index = 0, LPDWORD valType = NULL);

	virtual BOOL IsEmpty();
	virtual DWORD GetValueSize(LPCTSTR lpszValueName = NULL);

	virtual BOOL DeleteValue(LPCTSTR lpszValueName);

	virtual BOOL Open(HKEY hKey, LPCTSTR lpszKeyName, BOOL bCreate = TRUE);
	virtual HKEY GetHandle();
	virtual void Close();

protected:
	CString section;
};

/////////////////////////////////////////////////////////////////////////////

#endif
