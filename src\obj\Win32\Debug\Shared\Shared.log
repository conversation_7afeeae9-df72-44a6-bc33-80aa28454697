﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
cl : 命令行 warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  stdafx.cpp
c:\users\<USER>\desktop\eraser\src\shared\stdafx.cpp : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\shared\stdafx.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
cl : 命令行 warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  Utils.cpp
  UserInfo.cpp
c:\users\<USER>\desktop\eraser\src\shared\userinfo.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  SeException.cpp
  key.cpp
c:\users\<USER>\desktop\eraser\src\shared\key.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  FileHelper.cpp
c:\users\<USER>\desktop\eraser\src\shared\filehelper.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  正在生成代码...
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1397,5): warning MSB8012: TargetPath(C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Debug\Shared.lib) 与 Library 的 OutputFile 属性值(C:\Users\<USER>\Desktop\Eraser\src\Lib\OutD\Shared.lib)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Lib.OutputFile) 中指定的值匹配。
  Shared.vcxproj -> C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Debug\Shared.lib
