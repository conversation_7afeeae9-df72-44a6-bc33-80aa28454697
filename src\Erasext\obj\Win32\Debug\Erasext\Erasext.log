﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.Cpp.Common.props(209,5): warning MSB4211: 正在首次将属性“VC_ExecutablePath_x64_x64”设置为某个值，但已在“C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v170\Microsoft.Cpp.Analysis.props (23,5)”处使用了该属性。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(948,5): warning MSB4211: 正在首次将属性“GetTargetPathDependsOn”设置为某个值，但已在“C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\amd64\Microsoft.Common.CurrentVersion.targets (2243,5)”处使用了该属性。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
cl : 命令行 warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  StdAfx.cpp
cl : 命令行 warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  WipeProgDlg.cpp
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\filelockresolver.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  ErasextMenu.cpp
c:\users\<USER>\desktop\eraser\5.8.8\erasext\erasextmenu.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\5.8.8\shared\filehelper.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\filelockresolver.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\5.8.8\erasext\erasextmenu.cpp(486): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\erasextmenu.cpp(486): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\erasextmenu.cpp(486): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  Erasext.cpp
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\filelockresolver.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  ConfirmReplaceDlg.cpp
c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(93): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(93): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(93): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(163): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(163): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(163): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(162): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(162): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(162): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(169): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(169): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(169): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(168): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(168): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\erasext\confirmreplacedlg.cpp(168): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  ConfirmDialog.cpp
  正在生成代码...
ConfirmDialog.obj : warning LNK4075: 忽略“/EDITANDCONTINUE”(由于“/SAFESEH”规范)
    正在创建库 C:\Users\<USER>\Desktop\Eraser\5.8.8\Erasext\bin\Win32\Debug\Erasext.lib 和对象 C:\Users\<USER>\Desktop\Eraser\5.8.8\Erasext\bin\Win32\Debug\Erasext.exp
ConfirmDialog.obj : error LNK2019: 无法解析的外部符号 __imp__eraserShowOptions@8，该符号在函数 "protected: void __thiscall CConfirmDialog::OnOptions(void)" (?OnOptions@CConfirmDialog@@IAEXXZ) 中被引用
Erasext.obj : error LNK2019: 无法解析的外部符号 __imp__eraserInit@0，该符号在函数 "public: virtual int __thiscall CErasextApp::InitInstance(void)" (?InitInstance@CErasextApp@@UAEHXZ) 中被引用
Erasext.obj : error LNK2019: 无法解析的外部符号 __imp__eraserEnd@0，该符号在函数 "public: virtual int __thiscall CErasextApp::ExitInstance(void)" (?ExitInstance@CErasextApp@@UAEHXZ) 中被引用
ErasextMenu.obj : error LNK2019: 无法解析的外部符号 __imp__eraserRemoveFolder@12，该符号在函数 "public: virtual long __stdcall CErasextMenu::XMenuExt::InvokeCommand(struct _CMINVOKECOMMANDINFO *)" (?InvokeCommand@XMenuExt@CErasextMenu@@UAGJPAU_CMINVOKECOMMANDINFO@@@Z) 中被引用
ErasextMenu.obj : error LNK2019: 无法解析的外部符号 "__declspec(dllimport) public: __thiscall CFileLockResolver::~CFileLockResolver(void)" (__imp_??1CFileLockResolver@@QAE@XZ)，该符号在函数 "public: virtual __thiscall CEraserDlg::~CEraserDlg(void)" (??1CEraserDlg@@UAE@XZ) 中被引用
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: __thiscall CFileLockResolver::~CFileLockResolver(void)" (__imp_??1CFileLockResolver@@QAE@XZ)
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserCreateContext@4，该符号在函数 "protected: void __thiscall CEraserDlg::Erase(void)" (?Erase@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserDestroyContext@4，该符号在函数 "protected: void __thiscall CEraserDlg::OnDestroy(void)" (?OnDestroy@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserIsValidContext@4，该符号在函数 "protected: void __thiscall CEraserDlg::Erase(void)" (?Erase@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserSetDataType@8，该符号在函数 "protected: void __thiscall CEraserDlg::Erase(void)" (?Erase@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserAddItem@12，该符号在函数 "protected: void __thiscall CEraserDlg::Erase(void)" (?Erase@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserSetWindow@8，该符号在函数 "protected: void __thiscall CEraserDlg::Erase(void)" (?Erase@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserSetWindowMessage@8，该符号在函数 "protected: void __thiscall CEraserDlg::Erase(void)" (?Erase@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserDispFlags@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeBegin(void)" (?EraserWipeBegin@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserProgGetTimeLeft@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeUpdate(void)" (?EraserWipeUpdate@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserProgGetPercent@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeUpdate(void)" (?EraserWipeUpdate@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserProgGetTotalPercent@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeUpdate(void)" (?EraserWipeUpdate@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserProgGetCurrentPass@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeUpdate(void)" (?EraserWipeUpdate@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserProgGetPasses@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeUpdate(void)" (?EraserWipeUpdate@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserProgGetMessage@12，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeBegin(void)" (?EraserWipeBegin@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserProgGetCurrentDataString@12，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeBegin(void)" (?EraserWipeBegin@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserStart@4，该符号在函数 "protected: void __thiscall CEraserDlg::Erase(void)" (?Erase@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserStop@4，该符号在函数 "protected: virtual void __thiscall CEraserDlg::OnCancel(void)" (?OnCancel@CEraserDlg@@MAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserCompleted@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeDone(void)" (?EraserWipeDone@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserErrorStringCount@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeDone(void)" (?EraserWipeDone@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserFailedCount@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeDone(void)" (?EraserWipeDone@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 __imp__eraserShowReport@8，该符号在函数 "protected: int __thiscall CEraserDlg::EraserWipeDone(void)" (?EraserWipeDone@CEraserDlg@@IAEHXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 "__declspec(dllimport) public: __thiscall CFileLockResolver::CFileLockResolver(int)" (__imp_??0CFileLockResolver@@QAE@H@Z)，该符号在函数 "public: __thiscall CEraserDlg::CEraserDlg(class CWnd *)" (??0CEraserDlg@@QAE@PAVCWnd@@@Z) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 "__declspec(dllimport) public: void __thiscall CFileLockResolver::Close(void)" (__imp_?Close@CFileLockResolver@@QAEXXZ)，该符号在函数 "protected: void __thiscall CEraserDlg::OnDestroy(void)" (?OnDestroy@CEraserDlg@@IAEXXZ) 中被引用
WipeProgDlg.obj : error LNK2019: 无法解析的外部符号 "__declspec(dllimport) public: void __thiscall CFileLockResolver::SetHandle(unsigned long)" (__imp_?SetHandle@CFileLockResolver@@QAEXK@Z)，该符号在函数 "protected: void __thiscall CEraserDlg::Erase(void)" (?Erase@CEraserDlg@@IAEXXZ) 中被引用
C:\Users\<USER>\Desktop\Eraser\5.8.8\Erasext\bin\Win32\Debug\Erasext.dll : fatal error LNK1120: 29 个无法解析的外部命令
