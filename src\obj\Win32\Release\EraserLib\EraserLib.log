﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  StdAfx.cpp
  ByteEdit.cpp
  Common.cpp
  Custom.cpp
  CustomMethodEdit.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.h(85): error C3646: “m_pwfFunction”: 未知重写说明符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.h(85): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.h(96): error C3646: “m_pwfFunction”: 未知重写说明符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.h(96): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.h(188): error C2065: “m_pwfFunction”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.h(188): error C2039: “m_pwfFunction”: 不是“_Method”的成员
  c:\users\<USER>\desktop\eraser\src\eraserlib\pass.h(106): note: 参见“_Method”的声明
  DOD.cpp
  Eraser.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2076): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2076): note: "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2076): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(104): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2080): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2080): note: "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2080): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(104): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2084): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2084): note: "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2084): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(104): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2099): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2099): note: "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2099): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(104): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2105): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2105): note: "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2105): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(104): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2157): warning C4456: “_ctlState”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2127): note: 参见“_ctlState”的声明
c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2960): warning C4457: “filename”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2937): note: 参见“filename”的声明
c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(3007): warning C4456: “file”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserlib\eraser.cpp(2973): note: 参见“file”的声明
  FAT.cpp
  File.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\file.cpp(400): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\file.cpp(400): note: "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\file.cpp(400): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(104): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  FileLockResolver.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(161): error C2065: “CEraserContext”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(161): error C2061: 语法错误: 标识符“CEraserContext”
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(162): error C2065: “ectx”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(162): error C2660: “CFileLockResolver::HandleError”: 函数不接受 2 个参数
  c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(104): note: 参见“CFileLockResolver::HandleError”的声明
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(176): error C2065: “GUTMANN_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(179): error C2065: “DOD_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(182): error C2065: “DOD_E_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(185): error C2065: “RANDOM_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(188): error C2065: “FL2KB_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(191): error C2065: “SCHNEIER_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(176): error C2051: case 表达式不是常量
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(179): error C2051: case 表达式不是常量
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(182): error C2051: case 表达式不是常量
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(185): error C2051: case 表达式不是常量
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(188): error C2051: case 表达式不是常量
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(191): error C2051: case 表达式不是常量
c:\users\<USER>\desktop\eraser\src\eraserlib\filelockresolver.cpp(194): warning C4060: switch 语句没有包含“case”或“default”标签
  FillMemoryWith.cpp
  FirstLast2kb.cpp
  FreeSpace.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\freespace.cpp(559): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\freespace.cpp(559): note: "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\freespace.cpp(559): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(104): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  Gutmann.cpp
  NTFS.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\ntfs.cpp(637): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\ntfs.cpp(637): note: "ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\src\eraserlib\ntfs.cpp(637): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(104): note: 参见“ATL::CStringT<char,StrTraitMFC<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  OptionPages.cpp
  Options.cpp
  OptionsDlg.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\options.h(5): error C2065: “LibrarySettings”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\options.h(5): error C2059: 语法错误:“)”
c:\users\<USER>\desktop\eraser\src\eraserlib\options.h(7): error C2065: “LibrarySettings”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\options.h(7): error C2059: 语法错误:“)”
c:\users\<USER>\desktop\eraser\src\eraserlib\options.h(9): error C2065: “LibrarySettings”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\options.h(9): error C2059: 语法错误:“)”
c:\users\<USER>\desktop\eraser\src\eraserlib\optionsdlg.h(22): error C2079: “COptionsDlg::m_lsSettings”使用未定义的 struct“LibrarySettings”
c:\users\<USER>\desktop\eraser\src\eraserlib\optionsdlg.cpp(31): error C2664: “void COptionsForFiles::SetLibSettings(LibrarySettings *)”: 无法将参数 1 从“int *”转换为“LibrarySettings *”
  c:\users\<USER>\desktop\eraser\src\eraserlib\optionsdlg.cpp(31): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
c:\users\<USER>\desktop\eraser\src\eraserlib\optionsdlg.cpp(34): error C2440: “=”: 无法从“int *”转换为“LibrarySettings *”
  c:\users\<USER>\desktop\eraser\src\eraserlib\optionsdlg.cpp(34): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
  Pass.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\gutmann.h(5): error C2065: “CEraserContext”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\gutmann.h(5): error C2065: “context”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\dod.h(5): error C2065: “CEraserContext”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\dod.h(5): error C2065: “context”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\rnd.h(5): error C2065: “CEraserContext”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\rnd.h(5): error C2065: “context”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\custom.h(5): error C2065: “CEraserContext”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\custom.h(5): error C2065: “context”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\firstlast2kb.h(3): error C2065: “CEraserContext”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\firstlast2kb.h(3): error C2065: “context”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(16): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(16): error C2146: 语法错误: 缺少“;”(在标识符“passGutmann”的前面)
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(16): error C2065: “PASSES_GUTMANN”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(17): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(18): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(19): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(20): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(21): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(22): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(23): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(24): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(25): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(26): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(27): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(28): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(29): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(30): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(31): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(32): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(33): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(34): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(35): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(36): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(37): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(38): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(39): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(40): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(41): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(42): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(43): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(44): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(45): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(46): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(47): error C3861: “passThree”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(48): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(49): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(50): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(51): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(16): error C2078: 初始值设定项太多
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(18): error C2078: 初始值设定项太多
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(54): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(54): error C2086: “const int PASS”: 重定义
  c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(16): note: 参见“PASS”的声明
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(54): error C2146: 语法错误: 缺少“;”(在标识符“passDOD”的前面)
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(54): error C2065: “PASSES_DOD”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(55): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(56): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(57): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(58): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(59): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(60): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(61): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(54): error C2078: 初始值设定项太多
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(56): error C2078: 初始值设定项太多
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(64): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(64): error C2086: “const int PASS”: 重定义
  c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(16): note: 参见“PASS”的声明
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(64): error C2146: 语法错误: 缺少“;”(在标识符“passDOD_E”的前面)
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(64): error C2065: “PASSES_DOD_E”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(65): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(66): error C3861: “passOne”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(67): error C2065: “passRandom”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(64): error C2078: 初始值设定项太多
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(66): error C2078: 初始值设定项太多
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(72): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(72): error C2146: 语法错误: 缺少“;”(在标识符“bmMethods”的前面)
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(72): error C2065: “nBuiltinMethods”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(73): error C2065: “GUTMANN_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(75): error C2065: “PASSES_GUTMANN”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(73): error C3861: “bmEntry”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(80): error C2065: “DOD_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(82): error C2065: “PASSES_DOD”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(80): error C3861: “bmEntry”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(87): error C2065: “DOD_E_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(89): error C2065: “PASSES_DOD_E”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(87): error C3861: “bmEntry”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(94): error C2065: “RANDOM_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(96): error C2065: “PASSES_RND”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(94): error C3861: “bmEntry”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(101): error C2065: “FL2KB_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(103): error C2065: “PASSES_FL2KB”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(101): error C3861: “bmEntry”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(108): error C2065: “SCHNEIER_METHOD_ID”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(110): error C2065: “PASSES_SCHNEIER”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(108): error C3861: “bmEntry”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(72): error C2078: 初始值设定项太多
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(80): error C2078: 初始值设定项太多
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(116): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(116): error C2143: 语法错误: 缺少“;”(在“*”的前面)
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(116): error C2086: “const int BMETHOD”: 重定义
  c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(72): note: 参见“BMETHOD”的声明
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(117): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(117): error C2059: 语法错误:“{”
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(117): error C2143: 语法错误: 缺少“;”(在“{”的前面)
c:\users\<USER>\desktop\eraser\src\eraserlib\pass.cpp(117): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
  PassEditDlg.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\passeditdlg.cpp(50): error C2065: “PASSES_MAX”: 未声明的标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\passeditdlg.cpp(69): error C2065: “PASSES_MAX”: 未声明的标识符
  Random.cpp
  正在编译...
  ReportDialog.cpp
  RND.cpp
  sboxes.cpp
  Schneier7Pass.cpp
  SecManDlg.cpp
  SecurityManager.cpp
c:\users\<USER>\desktop\eraser\src\eraserlib\securitymanager.cpp(265): error C3861: “ansiToCString”: 找不到标识符
c:\users\<USER>\desktop\eraser\src\eraserlib\securitymanager.cpp(344): error C3861: “ansiToCString”: 找不到标识符
  tiger.cpp
