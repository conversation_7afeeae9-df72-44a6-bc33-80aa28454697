﻿#if !defined(AFX_REPORTDIALOG_H__E04E0B5A_267B_48B4_9EFF_A4321F890EC3__INCLUDED_)
#define AFX_REPORTDIALOG_H__E04E0B5A_267B_48B4_9EFF_A4321F890EC3__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

/////////////////////////////////////////////////////////////////////////////
// CReportDialog dialog

class CReportDialog : public CDialog
{
// Construction
public:
	CReportDialog(CWnd* pParent = NULL);   // standard constructor
    CStringArray *m_pstraErrorArray;

// Dialog Data
	//{{AFX_DATA(CReportDialog)
	enum { IDD = IDD_DIALOG_REPORT };
	CListCtrl	m_listErrors;
	CString	m_strStatistics;
	CString	m_strCompletion;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CReportDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CReportDialog)
	afx_msg void OnSaveAs();
	virtual BOOL OnInitDialog();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_REPORTDIALOG_H__E04E0B5A_267B_48B4_9EFF_A4321F890EC3__INCLUDED_)
