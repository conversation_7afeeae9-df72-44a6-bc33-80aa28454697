﻿#ifndef FILE_H
#define FILE_H

typedef class _DataStream {
public:
    _DataStream() {
        clear();
    }

    ~_DataStream() {
        clear();
    }

    CString m_strName;
    E_UINT64 m_uSize;
    bool m_bDefault;

private:
    void clear() {
        m_strName.Empty();
        m_uSize = 0;
        m_bDefault = false;
    }

} DataStream;

typedef CArray<DataStream, DataStream&> DataStreamArray;


bool
resetDate(HANDLE hFile);

bool
wipeFile(CEraserContext *context);

#endif
