﻿<?xml version="1.0" encoding="UTF-8"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8,00"
	Name="help"
	ProjectGUID="{79144ACA-6C48-42FD-99FB-F0794BF8DBE2}"
	RootNamespace="help"
	Keyword="Win32Proj"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				SubSystem="2"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<File
			RelativePath=".\html\about.htm"
			>
		</File>
		<File
			RelativePath=".\html\acknowledgements.htm"
			>
		</File>
		<File
			RelativePath=".\html\advanced.htm"
			>
		</File>
		<File
			RelativePath=".\html\allocated.htm"
			>
		</File>
		<File
			RelativePath=".\html\author.htm"
			>
		</File>
		<File
			RelativePath=".\html\basics.htm"
			>
		</File>
		<File
			RelativePath=".\html\basics.htm"
			>
		</File>
		<File
			RelativePath=".\html\close.htm"
			>
		</File>
		<File
			RelativePath=".\html\components.htm"
			>
		</File>
		<File
			RelativePath=".\html\conclusion.htm"
			>
		</File>
		<File
			RelativePath=".\html\configerase.htm"
			>
		</File>
		<File
			RelativePath=".\html\configgeneral.htm"
			>
		</File>
		<File
			RelativePath=".\html\contexthelp.htm"
			>
		</File>
		<File
			RelativePath=".\html\delete.htm"
			>
		</File>
		<File
			RelativePath=".\html\deleting.htm"
			>
		</File>
		<File
			RelativePath=".\html\dragndrop.htm"
			>
		</File>
		<File
			RelativePath=".\html\dram.htm"
			>
		</File>
		<File
			RelativePath=".\html\editmenu.htm"
			>
		</File>
		<File
			RelativePath=".\html\eraseall.htm"
			>
		</File>
		<File
			RelativePath=".\html\erasebrowsercache.htm"
			>
		</File>
		<File
			RelativePath=".\eraser.hhc"
			>
		</File>
		<File
			RelativePath=".\Eraser.hhk"
			>
		</File>
		<File
			RelativePath=".\Eraser.hhp"
			>
		</File>
		<File
			RelativePath=".\Eraser.hm"
			>
		</File>
		<File
			RelativePath=".\html\eraserbasics.htm"
			DeploymentContent="true"
			>
		</File>
		<File
			RelativePath=".\html\eraserecyclebin.htm"
			>
		</File>
		<File
			RelativePath=".\html\eraseswap.htm"
			>
		</File>
		<File
			RelativePath=".\html\eraseunused.htm"
			>
		</File>
		<File
			RelativePath=".\html\erasingfiles.htm"
			>
		</File>
		<File
			RelativePath=".\html\exit.htm"
			>
		</File>
		<File
			RelativePath=".\html\explorerbasics.htm"
			>
		</File>
		<File
			RelativePath=".\html\export.htm"
			>
		</File>
		<File
			RelativePath=".\html\faq.htm"
			>
		</File>
		<File
			RelativePath=".\html\filemenu.htm"
			>
		</File>
		<File
			RelativePath=".\html\further.htm"
			>
		</File>
		<File
			RelativePath=".\html\guttman.htm"
			>
		</File>
		<File
			RelativePath=".\html\help.htm"
			>
		</File>
		<File
			RelativePath=".\html\helpmenu.htm"
			>
		</File>
		<File
			RelativePath=".\html\homepage.htm"
			>
		</File>
		<File
			RelativePath=".\html\import.htm"
			>
		</File>
		<File
			RelativePath=".\html\index.htm"
			>
		</File>
		<File
			RelativePath=".\html\information.htm"
			>
		</File>
		<File
			RelativePath=".\html\informationbar.htm"
			>
		</File>
		<File
			RelativePath=".\html\introduction.htm"
			>
		</File>
		<File
			RelativePath=".\html\launcherbasics.htm"
			>
		</File>
		<File
			RelativePath=".\html\launchererasing.htm"
			>
		</File>
		<File
			RelativePath=".\html\launchersteps.htm"
			>
		</File>
		<File
			RelativePath=".\html\legal.htm"
			>
		</File>
		<File
			RelativePath=".\html\magnetic.htm"
			>
		</File>
		<File
			RelativePath=".\html\maximize.htm"
			>
		</File>
		<File
			RelativePath=".\html\minimize.htm"
			>
		</File>
		<File
			RelativePath=".\html\move.htm"
			>
		</File>
		<File
			RelativePath=".\html\newtask.htm"
			>
		</File>
		<File
			RelativePath=".\html\nohelp.htm"
			>
		</File>
		<File
			RelativePath=".\html\nohelp2.htm"
			>
		</File>
		<File
			RelativePath=".\html\ondemandentering.htm"
			>
		</File>
		<File
			RelativePath=".\html\ondemanderasing.htm"
			>
		</File>
		<File
			RelativePath=".\html\ondemandsteps.htm"
			>
		</File>
		<File
			RelativePath=".\html\open.htm"
			>
		</File>
		<File
			RelativePath=".\html\other.htm"
			>
		</File>
		<File
			RelativePath=".\html\overview.htm"
			>
		</File>
		<File
			RelativePath=".\html\overwritting.htm"
			>
		</File>
		<File
			RelativePath=".\html\paste.htm"
			>
		</File>
		<File
			RelativePath=".\html\processmenu.htm"
			>
		</File>
		<File
			RelativePath=".\html\progress.htm"
			>
		</File>
		<File
			RelativePath=".\html\properties.htm"
			>
		</File>
		<File
			RelativePath=".\html\pseudorandom.htm"
			>
		</File>
		<File
			RelativePath=".\html\ram.htm"
			>
		</File>
		<File
			RelativePath=".\html\recovery.htm"
			>
		</File>
		<File
			RelativePath=".\html\references.htm"
			>
		</File>
		<File
			RelativePath=".\html\refresh.htm"
			>
		</File>
		<File
			RelativePath=".\html\regulations.htm"
			>
		</File>
		<File
			RelativePath=".\html\report.htm"
			>
		</File>
		<File
			RelativePath=".\html\requirements.htm"
			>
		</File>
		<File
			RelativePath=".\html\restore.htm"
			>
		</File>
		<File
			RelativePath=".\html\run.htm"
			>
		</File>
		<File
			RelativePath=".\html\runall.htm"
			>
		</File>
		<File
			RelativePath=".\html\runningtasks.htm"
			>
		</File>
		<File
			RelativePath=".\html\schedule.htm"
			>
		</File>
		<File
			RelativePath=".\html\schedulerbasics.htm"
			>
		</File>
		<File
			RelativePath=".\html\schedulerenteringdata.htm"
			>
		</File>
		<File
			RelativePath=".\html\schedulerresults.htm"
			>
		</File>
		<File
			RelativePath=".\html\schedulersteps.htm"
			>
		</File>
		<File
			RelativePath=".\html\scrollbars.htm"
			>
		</File>
		<File
			RelativePath=".\html\secure.htm"
			>
		</File>
		<File
			RelativePath=".\html\securityproblems.htm"
			>
		</File>
		<File
			RelativePath=".\html\selectall.htm"
			>
		</File>
		<File
			RelativePath=".\html\shellext.htm"
			>
		</File>
		<File
			RelativePath=".\html\shellextbasics.htm"
			>
		</File>
		<File
			RelativePath=".\html\shellextenteringdata.htm"
			>
		</File>
		<File
			RelativePath=".\html\shellexterasing.htm"
			>
		</File>
		<File
			RelativePath=".\html\shellextmove.htm"
			>
		</File>
		<File
			RelativePath=".\html\shellextsteps.htm"
			>
		</File>
		<File
			RelativePath=".\html\sidestepping.htm"
			>
		</File>
		<File
			RelativePath=".\html\size.htm"
			>
		</File>
		<File
			RelativePath=".\html\special.htm"
			>
		</File>
		<File
			RelativePath=".\html\statistics.htm"
			>
		</File>
		<File
			RelativePath=".\html\statusbar.htm"
			>
		</File>
		<File
			RelativePath=".\html\statusbar.htm"
			>
		</File>
		<File
			RelativePath=".\html\statusbar2.htm"
			DeploymentContent="true"
			>
		</File>
		<File
			RelativePath=".\html\step1.htm"
			>
		</File>
		<File
			RelativePath=".\html\step2.htm"
			>
		</File>
		<File
			RelativePath=".\html\step3.htm"
			>
		</File>
		<File
			RelativePath=".\html\step4.htm"
			>
		</File>
		<File
			RelativePath=".\html\stepbystep.htm"
			>
		</File>
		<File
			RelativePath=".\html\stop.htm"
			>
		</File>
		<File
			RelativePath=".\html\support.htm"
			>
		</File>
		<File
			RelativePath=".\html\taskdata.htm"
			>
		</File>
		<File
			RelativePath=".\html\terms.htm"
			>
		</File>
		<File
			RelativePath=".\html\tips.htm"
			>
		</File>
		<File
			RelativePath=".\html\titlebar.htm"
			>
		</File>
		<File
			RelativePath=".\html\toolbar.htm"
			>
		</File>
		<File
			RelativePath=".\html\toolbar2.htm"
			>
		</File>
		<File
			RelativePath=".\html\trayenabled.htm"
			>
		</File>
		<File
			RelativePath=".\html\ui.htm"
			>
		</File>
		<File
			RelativePath=".\html\upgrading.htm"
			>
		</File>
		<File
			RelativePath=".\html\usdod.htm"
			>
		</File>
		<File
			RelativePath=".\html\viewlog.htm"
			>
		</File>
		<File
			RelativePath=".\html\viewmenu.htm"
			>
		</File>
		<File
			RelativePath=".\html\when.htm"
			>
		</File>
		<File
			RelativePath=".\html\why.htm"
			>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
