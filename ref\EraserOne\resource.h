//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by Eraser.rc
//
#define IDD_ABOUTBOX                    100
#define IDS_PREFERENCES_PROPSHT_CAPTION 104
#define IDD_PROPPAGE_ERASER             105
#define IDD_PROPPAGE_SCHEDULER          106
#define IDW_INFO_BAR                    107
#define IDR_ERASEREXPLORER              109
#define IDR_MAINFRAME                   128
#define IDR_ERASERTYPE                  129
#define IDS_PROPSHT_CAPTION             129
#define IDS_ERROR_NEW_TASK              130
#define IDS_QUESTION_CONFIRMATION       131
#define IDS_WARNING_NODATA              132
#define IDS_QUESTION_RUNALL             132
#define IDB_IMAGELIST_SMALL             133
#define IDS_ACTION_QUIT                 133
#define IDB_IMAGELIST_LARGE             134
#define IDS_ACTION_START                134
#define IDS_ACTION_RUN                  135
#define IDR_MENU_ERASERVIEW             136
#define IDS_ACTION_STOP                 136
#define IDR_MENU_SCHEDULERVIEW          137
#define IDS_ACTION_DONE                 137
#define IDD_PROPPAGE_TASKDATA           138
#define IDS_ACTION_DELETE               138
#define IDD_PROPPAGE_TASKSCHEDULE       139
#define IDS_ACTION_NEW                  139
#define IDD_PROPPAGE_TASKSTATISTICS     140
#define IDS_ACTION_ERROR                140
#define IDD_DIALOG_ERASER               141
#define IDS_ACTION_ERROR_FILE           141
#define IDI_ICON_TRAY                   142
#define IDS_ACTION_ERROR_FOLDER         142
#define IDR_MENU_TRAY                   143
#define IDS_ACTION_ERROR_UNUSED         143
#define IDI_ICON_TRAY_RUNNING           144
#define IDS_ERROR_TIMER                 144
#define IDI_ICON_TRAY_DISABLED          145
#define IDS_TOOLTIP_WAITING             145
#define IDR_CHILDFRAME                  146
#define IDS_TOOLTIP_NEXT                146
#define IDS_TOOLTIP_DISABLED            147
#define IDR_MENU_RDROP                  147
#define IDS_TOOLTIP_PROCESSING          148
#define IDB_HEADER                      149
#define IDS_SET_PROTECTION              149
#define IDS_INFO_RUNNING                150
#define IDS_ERROR_PREFERENCES_READ      151
#define IDS_ACTION_ENABLED              152
#define IDS_ACTION_DISABLED             153
#define IDS_INFO_NOLOG                  154
#define IDS_ERROR_VIEWLOG               155
#define IDS_ERROR_PREFERENCES_SAVE      156
#define IDS_QUESTION_INTERRUPT          157
#define IDS_INFO_QUEUED                 158
#define IDR_RT_RCDATA1                  158
#define IDS_CLEAR_PROTECTION            159
#define IDR_RT_RCDATA2                  160
#define IDD_DIALOG_HOTKEYS              171
#define IDD_DIALOG_KEYCOMBO             174
#define IDC_STATIC_VERSION              1000
#define IDC_HYPERLINK                   1001
#define IDC_HYPERLINK_MAIL              1002
#define IDC_RADIO_DISK                  1003
#define IDC_COMBO_DRIVES                1004
#define IDC_RADIO_FILES                 1005
#define IDC_EDIT_FOLDER                 1006
#define IDC_BUTTON_BROWSE               1007
#define IDC_CHECK_SUBFOLDERS            1008
#define IDC_CHECK_FOLDER                1009
#define IDC_CHECK_ONLYSUB               1010
#define IDC_RADIO_FILE                  1011
#define IDC_EDIT_FILE                   1012
#define IDC_BUTTON_BROWSE_FILES         1013
#define IDC_COMBO_WHEN                  1014
#define IDC_EDIT_TIME                   1015
#define IDC_CHECK_PM                    1016
#define IDC_STATIC_TIMESRAN             1017
#define IDC_CHECK_BOOT                  1017
#define IDC_STATIC_TIMESSUCCESSFUL      1018
#define IDC_STATIC_TIMESINTERRUPTED     1019
#define IDC_STATIC_TIMESFAILURE         1020
#define IDC_STATIC_AVEAREA              1021
#define IDC_STATIC_AVEWRITTEN           1022
#define IDC_STATIC_AVETIME              1023
#define IDC_STATIC_AVESPEED             1024
#define IDC_PROGRESS                    1025
#define IDC_STATIC_ERASING              1026
#define IDC_STATIC_MESSAGE              1027
#define IDC_STATIC_DATA                 1028
#define IDC_STATIC_PASS                 1029
#define IDC_STATIC_TIME                 1030
#define IDC_STATIC_PERCENT              1031
#define IDC_CHECK_STARTUP               1032
#define IDC_PROGRESS_TOTAL              1032
#define IDC_CHECK_LOG                   1033
#define IDC_STATIC_PERCENT_TOTAL        1033
#define IDC_CHECK_LOG_ONLYERRORS        1034
#define IDC_CHECK_LOG_LIMITSIZE         1035
#define IDC_EDIT_LIMIT                  1036
#define IDC_SPIN_LIMIT                  1037
#define IDC_CHECK_NOVISUALERRORS        1038
#define IDC_CHECK_CLEAR_SWAP            1039
#define IDC_CHECK_QUEUE                 1039
#define IDC_CHECK_SHELLEXT_RESULTS      1040
#define IDC_CHECK_ENABLE                1040
#define IDC_RADIO_ONDEMAND              1041
#define IDC_RADIO_SCHEDULER             1042
#define IDC_CHECK_RESULTSONLYWHENFAILED 1043
#define IDC_PERSISTENT_CHECK            1044
#define IDC_CHECK_RESULTS_FOR_FILES     1044
#define IDC_CHECK_NOTRAYICON            1045
#define IDC_CHECK_RESULTS_FOR_UNUSED_SPACE 1045
#define IDC_CHECK_WILDCARDS             1046
#define IDC_CHECK_HIDEONMINIMIZE        1046
#define IDC_BUTTON_RESET                1047
#define IDC_CHECK_WILDCARDS_SF          1047
#define IDC_STATIC_PAGINGFILE           1048
#define IDC_EDIT_STATISTICS             1049
#define IDC_STATIC_KB                   1050
#define IDC_PROGRESS1                   1051
#define IDC_CHECK_ERASEXT_ENABLE        1052
#define IDC_CHECK_PRNG_SLOWPOLL         1053
#define IDC_COMBO1                      1053
#define IDC_COMBO_WHENFINISH            1053
#define IDC_CHECK_RESOLVE_LOCK          1054
#define IDC_CHECK_RESOLVE_ASK_USR       1055
#define IDC_BUTTON_PROTECTION           1056
#define IDC_BUTTON_HOTKEYS              1057
#define IDC_LIST_HOTKEYS                1061
#define IDCHANGE                        1062
#define IDC_RADIO_MASK                  1064
#define IDC_EDIT_MASK                   1065
#define IDC_EDITTMP                     1066
#define IDC_BUTTON_OK                   1067
#define IDC_BUTTON_OKCHANGE             1068
#define ID_GFX_LARGEICON                32771
#define ID_GFX_SMALLICON                32772
#define ID_FILE_EXPORT                  32774
#define ID_FILE_IMPORT                  32776
#define ID_VIEW_INFO_BAR                32777
#define ID_FILE_NEW_TASK                32778
#define ID_EDIT_PROPERTIES              32779
#define ID_EDIT_DELETE_TASK             32780
#define ID_PROCESS_RUN                  32781
#define ID_PROCESS_STOP                 32782
#define ID_EDIT_PREFERENCES_ERASER      32783
#define ID_EDIT_PREFERENCES_GENERAL     32784
#define ID_TRAY_SHOW_WINDOW             32785
#define ID_TRAY_ENABLE                  32786
#define ID_FILE_VIEW_LOG                32787
#define ID_HELP_REWARD                  32788
#define ID_EDIT_REFRESH                 32789
#define ID_DRAG_MOVE                    32790
#define ID_DRAG_COPY                    32791
#define ID_DRAG_CANCEL                  32792
#define ID_PROCESS_RUNALL               32793
#define ID_INDICATOR_ITEMS              59142

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_3D_CONTROLS                     1
#define _APS_NEXT_RESOURCE_VALUE        177
#define _APS_NEXT_COMMAND_VALUE         32796
#define _APS_NEXT_CONTROL_VALUE         1069
#define _APS_NEXT_SYMED_VALUE           112
#endif
#endif
