# EraserLib 静态库使用指南

## 概述

EraserLib 已成功转换为静态库，提供了安全的文件删除功能。静态库版本具有以下优势：

- **独立部署**：无需额外的DLL文件
- **更好的性能**：减少了动态链接的开销
- **简化分发**：所有代码编译到最终可执行文件中
- **版本兼容性**：避免DLL版本冲突问题

## 项目配置变更

### 1. 配置类型变更
所有配置都已从 `DynamicLibrary` 改为 `StaticLibrary`：
- Debug|Win32
- Debug|x64  
- Release|Win32
- Release|x64
- Debug_Unicode|Win32
- Debug_Unicode|x64
- Release_Unicode|Win32
- Release_Unicode|x64
- Standalone Release|Win32
- Standalone Release|x64
- Standalone Release Unicode|Win32
- Standalone Release Unicode|x64

### 2. 预处理器定义变更
- 移除：`_DLL_ERASER`
- 添加：`_LIB_ERASER`

### 3. 运行时库变更
- Debug 配置：`MultiThreadedDebug` (/MTd)
- Release 配置：`MultiThreaded` (/MT)

### 4. MFC 设置
- 所有配置都使用静态 MFC 库

### 5. 输出文件
- 输出文件名：`EraserLib.lib`
- 预编译头文件：`EraserLib.pch`

## 使用方法

### 1. 项目设置

在您的项目中使用 EraserLib 静态库：

```cpp
// 1. 包含头文件
#include "EraserDll.h"

// 2. 链接库文件（在项目设置中添加）
#pragma comment(lib, "EraserLib.lib")
#pragma comment(lib, "netapi32.lib")  // 系统依赖库

// 3. 预处理器定义
#define _LIB_ERASER
```

### 2. 基本使用示例

```cpp
#include "EraserDll.h"
#include <iostream>

int main()
{
    ERASER_HANDLE handle;
    ERASER_RESULT result;
    
    // 初始化库
    result = eraserInit();
    if (eraserError(result)) {
        std::cout << "初始化失败" << std::endl;
        return -1;
    }
    
    // 创建擦除上下文
    result = eraserCreateContext(&handle);
    if (eraserError(result)) {
        std::cout << "创建上下文失败" << std::endl;
        eraserEnd();
        return -1;
    }
    
    // 设置数据类型为文件
    result = eraserSetDataType(handle, ERASER_DATA_FILES);
    
    // 添加要擦除的文件
    const char* filename = "C:\\temp\\secret.txt";
    result = eraserAddItem(handle, (LPVOID)filename, strlen(filename));
    
    // 开始擦除
    result = eraserStartSync(handle);
    if (eraserOK(result)) {
        std::cout << "文件擦除成功" << std::endl;
    }
    
    // 清理资源
    eraserDestroyContext(handle);
    eraserEnd();
    
    return 0;
}
```

### 3. 高级功能

```cpp
// 使用特定擦除方法
result = eraserCreateContextEx(&handle, 
                              convEraseMethod(ERASER_METHOD_GUTMANN),
                              35,  // 35遍擦除
                              fileClusterTips | fileNames);

// 设置进度通知
result = eraserSetWindow(handle, hwndProgress);

// 异步擦除
result = eraserStart(handle);

// 检查运行状态
E_UINT8 running;
eraserIsRunning(handle, &running);

// 获取进度
E_UINT8 percent;
eraserProgGetPercent(handle, &percent);
```

## 编译说明

### 1. 编译 EraserLib 静态库

```bash
# 使用 Visual Studio 命令行
msbuild EraserLib.vcxproj /p:Configuration=Release /p:Platform=Win32

# 或者在 Visual Studio IDE 中
# 选择 Release|Win32 配置并生成项目
```

### 2. 输出文件位置

编译成功后，静态库文件位于：
```
bin\Win32\Release\EraserLib.lib
bin\x64\Release\EraserLib.lib
```

### 3. 测试程序

项目包含一个测试程序 `TestStaticLib.cpp`，演示了基本的使用方法：

```bash
# 编译测试程序（需要先编译 EraserLib）
cl TestStaticLib.cpp /I. /link EraserLib.lib netapi32.lib
```

## 依赖库

使用 EraserLib 静态库时，您的项目需要链接以下库：

### 必需库
- `EraserLib.lib` - 主要的擦除功能库
- `netapi32.lib` - Windows 网络API库

### 可选库（根据功能需要）
- `EraserUI.lib` - 用户界面组件（如果使用UI功能）
- `Shared.lib` - 共享组件库

## 注意事项

### 1. 线程安全
- EraserLib 支持多线程使用
- 每个线程应使用独立的 ERASER_HANDLE

### 2. 错误处理
- 始终检查函数返回值
- 使用 `eraserOK()` 和 `eraserError()` 宏进行错误检查

### 3. 资源管理
- 必须调用 `eraserInit()` 初始化库
- 必须调用 `eraserEnd()` 清理资源
- 每个 `eraserCreateContext()` 都要对应 `eraserDestroyContext()`

### 4. 权限要求
- 擦除系统文件可能需要管理员权限
- 确保对目标文件有写入权限

## 支持的擦除方法

- `ERASER_METHOD_LIBRARY` - 库默认方法
- `ERASER_METHOD_GUTMANN` - Gutmann 35遍擦除（最安全）
- `ERASER_METHOD_DOD` - 美国国防部标准
- `ERASER_METHOD_DOD_E` - 美国国防部扩展方法
- `ERASER_METHOD_PSEUDORANDOM` - 伪随机数据擦除
- `ERASER_METHOD_FIRST_LAST_2KB` - 仅擦除文件首末2KB
- `ERASER_METHOD_SCHNEIER` - Schneier 7遍擦除

## 故障排除

### 编译错误
1. 确保预处理器定义包含 `_LIB_ERASER`
2. 检查运行时库设置（应为 /MT 或 /MTd）
3. 确保使用静态 MFC 库

### 链接错误
1. 确保链接了所有必需的库文件
2. 检查库文件路径是否正确
3. 确保目标平台匹配（Win32/x64）

### 运行时错误
1. 检查是否调用了 `eraserInit()`
2. 验证文件路径和权限
3. 确保正确的错误处理

## 版本信息

- 基于 Eraser 5.8.8
- 静态库版本：1.0
- 支持平台：Windows XP 及以上
- 编译器：Visual Studio 2019/2022

## 许可证

本项目遵循 GNU General Public License v2.0 许可证。
详细信息请参阅 COPYING.txt 文件。
