﻿// Erasext.h
#ifndef ERASEXT_H
#define ERASEXT_H

#ifndef __AFXWIN_H__
    #error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"       // main symbols

/////////////////////////////////////////////////////////////////////////////
// CErasextApp
// See Erasext.cpp for the implementation of this class
//

class CErasextApp : public CWinApp
{
public:
    CErasextApp();

// Overrides
    // ClassWizard generated virtual function overrides
    //{{AFX_VIRTUAL(CErasextApp)
	public:
    virtual BOOL InitInstance();
	virtual int ExitInstance();
	//}}AFX_VIRTUAL

    //{{AFX_MSG(CErasextApp)
        // NOTE - the ClassWizard will add and remove member functions here.
        //    DO NOT EDIT what you see in these blocks of generated code !
    //}}AFX_MSG
    DECLARE_MESSAGE_MAP()
};


/////////////////////////////////////////////////////////////////////////////

#endif
