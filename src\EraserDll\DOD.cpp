﻿// DOD.cpp - 美国国防部标准擦除算法实现
//
// 本文件实现了美国国防部（Department of Defense）标准的安全擦除算法
// DOD 5220.22-M 标准规定了7遍擦除过程，确保数据无法通过常规手段恢复
//
// 擦除模式：
// 遍数1: 随机字符
// 遍数2: 遍数1的补码
// 遍数3: 固定模式（通常为0x00）
// 遍数4: 随机字符
// 遍数5: 随机字符
// 遍数6: 遍数5的补码
// 遍数7: 验证遍数（通常为0x00）

#include "stdafx.h"
#include "EraserDll.h"
#include "Common.h"
#include "DOD.h"

// DOD标准中使用随机数据的遍数数量
const E_UINT32 DOD_RANDOM_PASSES = 3;

/**
 * 使用美国国防部标准擦除文件
 * @param context 擦除上下文指针
 * @return bool 成功返回true，失败返回false
 *
 * 功能说明：
 * - 实现DOD 5220.22-M标准的7遍擦除算法
 * - 使用随机数据和其补码确保彻底覆盖
 * - 支持测试模式，可在每遍后暂停检查结果
 * - 提供实时进度更新和统计信息
 */
bool
wipeFileWithDoD(CEraserContext *context)
{
    LPPASS   passDOD    = context->m_lpmMethod->m_lpPasses;  // DOD擦除遍数数组
    E_UINT32 uStartTime = GetTickCount();                   // 开始时间（用于统计）
    E_UINT32 uUsedSize  = 0;                                // 当前使用的缓冲区大小
    E_UINT32 uSavedSize = 0;                                // 保存的缓冲区大小
    E_UINT64 uLength    = 0;                                // 剩余要擦除的长度
    E_UINT64 uWiped     = 0;                                // 已擦除的总字节数
    E_UINT32 uWritten   = 0;                                // 实际写入的字节数
    E_UINT8  uRandomArray[DOD_RANDOM_PASSES];               // 随机数据数组
    bool     bCompleted = true;                             // 操作完成标志

    // 发送开始通知（仅发送一次）
    postStartNotification(context);

    // 设置缓冲区大小
    setBufferSize(context, uSavedSize);

    // 为遍数1、4、5生成随机字符
    // 这些遍数使用随机数据来确保原始数据被彻底覆盖
    isaacFill(uRandomArray, DOD_RANDOM_PASSES);

    setPassOne(passDOD[0], (E_UINT16)uRandomArray[0]);  // 遍数1：随机字符
    setPassOne(passDOD[3], (E_UINT16)uRandomArray[1]);  // 遍数4：随机字符
    setPassOne(passDOD[4], (E_UINT16)uRandomArray[2]);  // 遍数5：随机字符

    // 清零随机数组（安全考虑）
    ZeroMemory(uRandomArray, DOD_RANDOM_PASSES);

    // 设置遍数2和6为遍数1和5的补码
    // 补码确保每个位都被0和1覆盖过
    setPassOne(passDOD[1], (E_UINT16)(~(passDOD[0].byte1) & 0x00FF));  // 遍数2：遍数1的补码
    setPassOne(passDOD[5], (E_UINT16)(~(passDOD[4].byte1) & 0x00FF));  // 遍数6：遍数5的补码

    // 主擦除循环：执行DOD标准的7遍擦除
    for (E_UINT16 uCurrentPass = 0; uCurrentPass < PASSES_DOD; uCurrentPass++) {
        // 更新当前遍数（用于进度显示）
        eraserSafeAssign(context, context->m_uProgressCurrentPass, (E_UINT16)(uCurrentPass + 1));

        // 重新定位到文件开始位置
        // 每一遍都要从头开始覆盖整个文件
        SetFilePointer(context->m_hFile, context->m_uiFileStart.LowPart,
                       (E_PINT32)&context->m_uiFileStart.HighPart, FILE_BEGIN);

        uLength = context->m_uiFileSize.QuadPart;  // 重置剩余长度
        uUsedSize = uSavedSize;                    // 重置缓冲区大小

        // 用当前遍数的数据模式填充缓冲区
        fillPassData(context->m_puBuffer, uUsedSize, &passDOD[uCurrentPass]);

        // 内层循环：逐块写入数据直到整个文件被覆盖
        while (uLength > 0) {
            // 如果是随机数据遍数，需要重新填充随机数据
            // 这确保每次写入的都是不同的随机数据
            if (isRandomPass(passDOD[uCurrentPass])) {
                isaacFill((E_PUINT8)context->m_puBuffer, uUsedSize);
            }

            // 调整缓冲区大小以适应剩余数据
            // 最后一块可能小于完整缓冲区大小
            if (uLength < (E_UINT64)uUsedSize) {
                uUsedSize = (E_UINT32)uLength;
            }

            // 执行写入操作
            // 只有在未被终止且写入成功时才认为完成
            bCompleted = !eraserInternalTerminated(context) &&
                         WriteFile(context->m_hFile, context->m_puBuffer,
                                   uUsedSize, &uWritten, NULL) &&
                         (uUsedSize == uWritten);

            // 强制将数据刷新到磁盘
            // 确保数据真正写入存储介质而不是停留在缓存中
            FlushFileBuffers(context->m_hFile);

            // 如果写入失败或被终止，停止操作
            if (!bCompleted) {
                break;
            }

            // 更新统计信息
            context->m_uProgressWiped += (E_UINT64)uUsedSize;  // 总擦除字节数
            uWiped += (E_UINT64)uUsedSize;                     // 本次擦除字节数

            // 更新剩余长度
            uLength -= (E_UINT64)uUsedSize;

            // 发送进度更新通知
            postUpdateNotification(context, PASSES_DOD);
        }

        // 测试模式：在每遍完成后暂停
        // 允许用户检查擦除结果的有效性
        if (context->m_uTestMode && !eraserInternalTerminated(context)) {
            context->m_evTestContinue.ResetEvent();     // 重置继续事件
            eraserTestPausedNotify(context);            // 通知测试暂停
            WaitForSingleObject(context->m_evTestContinue, INFINITE);  // 等待继续信号
        }

        // 如果当前遍数未完成，退出整个擦除过程
        if (!bCompleted) {
            break;
        }
    }

    // 设置最终统计信息
    setEndStatistics(context, uWiped, uStartTime);

    // 清零遍数数组以防止敏感信息泄露
    // 这是一个重要的安全措施，确保使用的随机数据不会残留在内存中
    setPassOne(passDOD[0], 0);  // 清零遍数1的随机数据
    setPassOne(passDOD[1], 0);  // 清零遍数2的补码数据
    setPassOne(passDOD[3], 0);  // 清零遍数4的随机数据
    setPassOne(passDOD[4], 0);  // 清零遍数5的随机数据
    setPassOne(passDOD[5], 0);  // 清零遍数6的补码数据

    return bCompleted;
}

// ============================================================================
// 文件结束
//
// 本文件实现了美国国防部DOD 5220.22-M标准的安全擦除算法。
// 该标准通过7遍不同模式的数据覆写，确保原始数据无法通过
// 常规的数据恢复技术恢复。
//
// 算法特点：
// - 使用随机数据和其补码确保每个位都被0和1覆盖
// - 支持大文件的分块处理
// - 提供实时进度监控
// - 包含测试模式用于验证擦除效果
// - 严格的内存清理防止信息泄露
//
// 该算法广泛应用于政府和军事机构的敏感数据销毁。
// ============================================================================
