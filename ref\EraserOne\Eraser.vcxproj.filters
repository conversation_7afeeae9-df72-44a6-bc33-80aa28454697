﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{ec5668ae-d769-49fb-b195-e1a48cb2fbbb}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{2e1a7735-3471-403d-9ce3-be6ed75fd26e}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{d7e9f17b-b230-4a7b-a1f6-677417b6316b}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ChildFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DateTimeInit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DropTargetWnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Eraser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="EraserDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="EraserDoc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="EraserView.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HotKeyDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HotKeyListCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Item.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KeyComboDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MainFrm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="OleTreeCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PreferencesPage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PreferencesSheet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SchedulerView.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ShellListView.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ShellTree.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SystemTray.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TaskDataPage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TaskPropertySheet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Eraser.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ChildFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DateTimeInit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DropTargetWnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Eraser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EraserDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EraserDoc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EraserView.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HotKeyDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HotKeyListCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Item.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KeyComboDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MainFrm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="OleTreeCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PreferencesPage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PreferencesSheet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SchedulerView.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShellListView.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShellTree.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SystemTray.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TaskDataPage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TaskPropertySheet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\dragging.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Eraser.rc2">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\icr_hand.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\nodraggi.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\tfdropcopy.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\tfnodrop.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\tfnodropcopy.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\tfnodropmove.cur">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\Eraser.ico">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\EraserDoc.ico">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\ftnetwork.bmp">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\Header.bmp">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\ico00001.ico">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\ico00002.ico">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\icon_tra.ico">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\imageList.bmp">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\MenuCheck.bmp">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\MenuCheckSelected.bmp">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\Toolbar.bmp">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\toolbar1.bmp">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="res\treeImageList.bmp">
      <Filter>Resource Files</Filter>
    </Image>
  </ItemGroup>
</Project>