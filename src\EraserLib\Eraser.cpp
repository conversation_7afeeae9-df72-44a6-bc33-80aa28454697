﻿// Eraser.cpp - Eraser 动态链接库主实现文件
//
// 本文件实现了 Eraser 安全删除库的所有导出函数和核心功能
// 包括上下文管理、擦除控制、进度监控、统计信息等
//
// 主要功能模块：
// - 库初始化和清理
// - 上下文创建和销毁
// - 数据类型和项目管理
// - 擦除操作控制（启动、停止、监控）
// - 进度信息和统计数据
// - 错误处理和结果查询
// - UI 对话框显示
// - 文件和目录删除工具

#include "stdafx.h"
#include "Eraser.h"
#include "EraserDll.h"
#include "Common.h"

// 选项和对话框相关头文件
#include "Options.h"
#include "OptionsDlg.h"
#include "ReportDialog.h"

// 擦除算法实现头文件
#include "RND.h"           // 随机数据擦除
#include "DOD.h"           // 美国国防部标准擦除
#include "Gutmann.h"       // Gutmann 35遍擦除
#include "Custom.h"        // 自定义擦除方法

// 文件系统和存储相关头文件
#include "File.h"          // 文件操作
#include "NTFS.h"          // NTFS 文件系统特殊处理
#include "FreeSpace.h"     // 未使用空间擦除
#include "FAT.h"           // FAT 文件系统处理

// 外部依赖头文件
#include "..\EraserUI\VisualStyles.h"    // 视觉样式支持
#include "..\shared\FileHelper.h"        // 文件辅助函数
#include "..\shared\key.h"               // 密钥相关功能

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

// 重新定义 MAX_PATH 以支持 Vista 下的长文件名
// Vista 系统下某些文件名可能超过标准的 260 字符限制
#undef MAX_PATH
#define MAX_PATH 2048

// ============================================================================
// CEraserDll 应用程序类 - MFC DLL 主类
// ============================================================================

// MFC 消息映射表（当前为空，由 ClassWizard 自动生成）
BEGIN_MESSAGE_MAP(CEraserDll, CWinApp)
    //{{AFX_MSG_MAP(CEraserApp)
        // 注意：ClassWizard 会在此处添加和删除映射宏
        // 不要编辑这些自动生成的代码块！
    //}}AFX_MSG_MAP
END_MESSAGE_MAP()

// ============================================================================
// CEraserDll 构造和初始化
// ============================================================================

/**
 * CEraserDll 构造函数
 * 设置结构化异常处理转换器，将系统异常转换为 C++ 异常
 */
CEraserDll::CEraserDll()
{
    _set_se_translator(SeTranslator);  // 设置结构化异常转换器
}

/**
 * DLL 实例初始化函数
 * 在 DLL 被加载时调用，执行必要的初始化工作
 */
BOOL CEraserDll::InitInstance()
{
    // 检测操作系统版本
    OSVERSIONINFO ov;
    ZeroMemory(&ov, sizeof(OSVERSIONINFO));
    ov.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
    GetVersionEx(&ov);

    // 判断是否为 Windows NT 系列系统（NT4.0 及以上版本）
    // 这影响某些文件系统特性的可用性
    //isWindowsNT = (ov.dwPlatformId == VER_PLATFORM_WIN32_NT);
	//isWindowsNT =  (ov.dwPlatformId == VER_PLATFORM_WIN32_NT  && (ov.dwMajorVersion > 5 || (ov.dwMajorVersion == 5 && ov.dwMinorVersion >= 1)));
	isWindowsNT = (ov.dwPlatformId == VER_PLATFORM_WIN32_NT  && (ov.dwMajorVersion >= 4));

    // 初始化上下文数组
    // 上下文数组用于管理多个并发的擦除操作
    eraserContextArrayAccess();
    ZeroMemory(eraserContextArray, sizeof(CEraserContext*) * (ERASER_MAX_CONTEXT + 1));

    // 初始化库引用计数器
    eraserLibraryUnlock();

    return CWinApp::InitInstance();
}

/**
 * DLL 实例退出函数
 * 在 DLL 被卸载时调用，执行清理工作
 */
int CEraserDll::ExitInstance()
{
    // 清理资源
    eraserLibraryUnlock();  // 减少库引用计数
    eraserEnd();            // 执行库清理

    return CWinApp::ExitInstance();
}


// ============================================================================
// 全局对象
// ============================================================================

// 唯一的 CEraserDll 应用程序对象实例
CEraserDll theApp;

// ============================================================================
// 函数声明
// ============================================================================

// 擦除线程函数声明
UINT eraserThread(LPVOID);

// ============================================================================
// 辅助函数
// ============================================================================

/**
 * 自定义睡眠函数，在等待期间处理消息队列
 * @param second 睡眠时间（秒）
 *
 * 功能说明：
 * - 在睡眠期间继续处理 Windows 消息，防止界面冻结
 * - 使用高精度时钟计时，确保准确的睡眠时间
 */
static void mySleep(UINT second)
{
    MSG  msg;
    // 处理消息队列中的所有待处理消息
    while( PeekMessage( &msg, NULL, 0, 0, PM_REMOVE ) )
    {
        GetMessage( &msg, NULL, 0, 0 );
        TranslateMessage(&msg);  // 转换键盘消息
        DispatchMessage(&msg);   // 分发消息到窗口过程
    }

    // 使用高精度时钟进行精确计时
    clock_t start, finish;
    double  duration;
    start = clock();
    for(;;)
    {
        finish = clock();
        duration = (double)(finish - start) / CLOCKS_PER_SEC;
        if(duration > second)
            break;
    }
}

/**
 * 安全覆写文件名
 * @param szFile 原始文件路径
 * @param szLastFileName 输出缓冲区，存储最终的文件名
 * @return 成功返回true，失败返回false
 *
 * 功能说明：
 * - 通过多次重命名文件来覆盖原始文件名，防止文件名恢复
 * - 使用随机字符替换文件名中的每个字符（保留扩展名分隔符'.'）
 * - 执行多遍覆写以确保彻底清除文件名信息
 * - 处理防病毒软件可能造成的文件访问延迟
 */
static inline bool
overwriteFileName(LPCTSTR szFile, LPTSTR szLastFileName)
{
    TCHAR szNewName[MAX_PATH];      // 新文件名缓冲区
    PTCHAR pszLastSlash;            // 最后一个反斜杠位置
    size_t index, i, j, length;     // 索引和长度变量

    try {
        // 复制原始文件路径
        _tcsncpy(szLastFileName, szFile, MAX_PATH);
        // 找到最后一个反斜杠（文件名开始位置）
        pszLastSlash = _tcsrchr(szLastFileName, '\\');

        if (pszLastSlash == NULL) {
            return false;  // 无效路径
        }

        // 计算文件名开始的索引位置
        index = (pszLastSlash - szLastFileName) / sizeof(TCHAR);

        _tcsncpy(szNewName, szLastFileName, MAX_PATH);
        length = (E_UINT32)_tcslen(szLastFileName);

        // 执行多遍文件名覆写
        for (i = 0; i < ERASER_FILENAME_PASSES; i++) {
            // 用随机数据填充文件名部分（不包括路径）
            isaacFill((E_PUINT8)(szNewName + index + 1), (length - index - 1) * sizeof(TCHAR));

            // 将随机数据转换为安全的文件名字符
            for (j = index + 1; j < length; j++) {
                if (szLastFileName[j] != '.') {
                    // 非点号字符用安全字符数组中的字符替换
                    szNewName[j] = ERASER_SAFEARRAY[((E_UINT16)szNewName[j]) % ERASER_SAFEARRAY_SIZE];
                } else {
                    // 保留扩展名分隔符
                    szNewName[j] = '.';
                }
            }

            // 尝试重命名文件
            if (MoveFile(szLastFileName, szNewName)) {
                _tcsncpy(szLastFileName, szNewName, MAX_PATH);
            } else {
                // 如果重命名失败，等待50毫秒后重试
                // 这是为了处理防病毒软件可能造成的文件锁定
                Sleep(50);
                if (MoveFile(szLastFileName, szNewName)) {
                    _tcsncpy(szLastFileName, szNewName, MAX_PATH);
                }
            }
        }

        return true;
    }
    catch (...) {
        ASSERT(0);  // 调试模式下触发断言
    }

    // 清理敏感数据
    ZeroMemory(szNewName, MAX_PATH * sizeof(TCHAR));

    return false;
}

/**
 * 检查文件夹是否为空
 * @param szFolder 要检查的文件夹路径
 * @return 空文件夹返回true，非空或错误返回false
 *
 * 功能说明：
 * - 检查指定文件夹是否不包含任何文件或子文件夹
 * - 忽略特殊目录项"."和".."
 * - 用于确定文件夹是否可以安全删除
 */
static inline bool
isFolderEmpty(LPCTSTR szFolder)
{
    bool            bEmpty = true;          // 假设文件夹为空
    HANDLE          hFind;                  // 文件查找句柄
    WIN32_FIND_DATA wfdData;                // 文件查找数据
    CString         strFolder(szFolder);    // 文件夹路径字符串

    // 确保文件夹路径以反斜杠结尾
    if (strFolder[strFolder.GetLength() - 1] != '\\') {
        strFolder += "\\";
    }

    // 开始查找文件夹中的所有项目
    hFind = FindFirstFile((LPCTSTR)(strFolder + _T("*")), &wfdData);

    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            // 跳过特殊目录项"."和".."
            if (bitSet(wfdData.dwFileAttributes, FILE_ATTRIBUTE_DIRECTORY) &&
                ISNT_SUBFOLDER(wfdData.cFileName)) {
                continue;  // 这是"."或".."，跳过
            }

            // 发现了实际的文件或子文件夹
            bEmpty = false;
            break;
        }
        while (FindNextFile(hFind, &wfdData));

        VERIFY(FindClose(hFind));  // 关闭查找句柄
    }
    return bEmpty;
}

/**
 * 清空文件夹中的所有内容
 * @param szFolder 要清空的文件夹路径
 * @return 成功清空返回true，失败返回false
 *
 * 警告：此函数会删除指定文件夹中的所有文件和子文件夹，使用时需谨慎！
 *
 * 功能说明：
 * - 递归删除文件夹中的所有文件和子文件夹
 * - 使用安全删除方法确保数据无法恢复
 * - 跳过特殊目录项"."和".."
 * - 如果任何项目删除失败，整个操作被标记为失败
 */
static inline bool
emptyFolder(LPCTSTR szFolder)
{
    bool            bEmpty = true;          // 操作成功标志
    HANDLE          hFind;                  // 文件查找句柄
    WIN32_FIND_DATA wfdData;                // 文件查找数据
    CString         strFolder(szFolder);    // 文件夹路径
    CString         strFile;                // 当前处理的文件路径

    // 确保文件夹路径以反斜杠结尾
    if (strFolder[strFolder.GetLength() - 1] != '\\') {
        strFolder += "\\";
    }

    // 开始枚举文件夹中的所有项目
    hFind = FindFirstFile((LPCTSTR)(strFolder + _T("*")), &wfdData);

    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            // 构造完整的文件/文件夹路径
            strFile = strFolder + wfdData.cFileName;

            if (bitSet(wfdData.dwFileAttributes, FILE_ATTRIBUTE_DIRECTORY)) {
                // 这是一个子文件夹
                if (IS_SUBFOLDER(wfdData.cFileName)) {
                    // 递归删除子文件夹（跳过"."和".."）
                    if (eraserError(eraserRemoveFolder((LPVOID)(LPCTSTR)strFile,
                            (E_UINT16)strFile.GetLength(), ERASER_REMOVE_RECURSIVELY))) {
                        bEmpty = false;  // 删除失败
                    }
                }
            } else {
                // 这是一个文件，使用安全删除
                if (eraserError(eraserRemoveFile((LPVOID)(LPCTSTR)strFile,
                        (E_UINT16)strFile.GetLength()))) {
                    bEmpty = false;  // 删除失败
                }
            }
        }
        while (FindNextFile(hFind, &wfdData));

        VERIFY(FindClose(hFind));  // 关闭查找句柄
    } else {
        return false;  // 无法打开文件夹
    }
    return bEmpty;
}

// ============================================================================
// 上下文辅助函数
// ============================================================================

/**
 * 将上下文句柄转换为上下文对象指针
 * @param param1 上下文句柄
 * @param pointer 输出参数，返回上下文对象指针
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 验证句柄的有效性和安全性
 * - 检查上下文ID和线程所有权
 * - 防止跨线程访问和无效访问
 * - 时间复杂度为O(1)（不计算错误检查）
 */
static inline ERASER_RESULT
contextToAddress(E_IN ERASER_HANDLE param1, E_OUT CEraserContext **pointer)
{
    // 验证上下文句柄的基本有效性
    if (!eraserContextOK(param1)) {
        return ERASER_ERROR_PARAM1;
    } else if (!AfxIsValidAddress(pointer, sizeof(CEraserContext*))) {
        return ERASER_ERROR_PARAM2;
    } else {
        try {
            // 从句柄中提取数组索引
            E_UINT16 index = eraserContextIndex(param1);
            eraserContextArrayAccess();
            *pointer = eraserContextArray[index];

            if (*pointer == 0) {
                // 上下文已被删除
                return ERASER_ERROR_PARAM1;
            } else if (!AfxIsValidAddress(*pointer, sizeof(CEraserContext))) {
                // 上下文内存已损坏，清理并返回错误
                eraserContextArray[index] = 0;
                *pointer = 0;
                return ERASER_ERROR_PARAM1;
            } else if ((*pointer)->m_uContextID != eraserContextID(param1) ||
                       (*pointer)->m_uOwnerThreadID != ::GetCurrentThreadId()) {
                // 上下文ID不匹配或尝试从其他线程访问
                *pointer = 0;
                return ERASER_ERROR_DENIED;
            }
        } catch (...) {
            ASSERT(0);  // 调试模式下触发断言
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

// ============================================================================
// 导出函数实现
// ============================================================================

// ============================================================================
// 库初始化和清理函数
// ============================================================================

/**
 * 初始化 Eraser 库
 * 必须在使用库的其他功能之前调用此函数
 */
ERASER_EXPORT
eraserInit()
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserInit\n");

    try {
        // 增加库引用计数器
        eraserLibraryInit();
        // 初始化随机数生成器
        randomInit();

        return ERASER_OK;
    } catch (...) {
        ASSERT(0);
        return ERASER_ERROR_EXCEPTION;
    }
}

/**
 * 清理 Eraser 库资源
 * 程序结束前应调用此函数释放库占用的资源
 */
ERASER_EXPORT
eraserEnd()
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserEnd\n");

    ERASER_RESULT result = ERASER_OK;

    // 减少库引用计数器
    eraserLibraryUninit();

    eraserContextArrayAccess();

    try {
        // 如果没有其他实例在使用库，执行清理工作
        if (!eraserIsLibraryInit()) {
            // 遍历所有上下文并清理
            for (ERASER_HANDLE i = ERASER_MIN_CONTEXT; i <= ERASER_MAX_CONTEXT; i++) {
                if (eraserContextArray[i] != 0) {
                    if (AfxIsValidAddress(eraserContextArray[i], sizeof(CEraserContext))) {
                        try {
                            // 停止上下文的非同步访问
                            VERIFY(eraserOK(eraserStop(i)));
                            eraserContextLock(eraserContextArray[i]);
                            delete eraserContextArray[i];
                        } catch (...) {
                            ASSERT(0);
                            result = ERASER_ERROR_EXCEPTION;
                        }
                    }
                    eraserContextArray[i] = 0;
                }
            }
        }

        // 减少伪随机数生成器引用计数
        randomEnd();
    } catch (...) {
        ASSERT(0);
        result = ERASER_ERROR_EXCEPTION;
    }

    return result;
}


// ============================================================================
// 上下文创建和销毁函数
// ============================================================================

/**
 * 使用预定义设置创建擦除上下文
 * 创建一个新的擦除操作上下文，使用库的默认设置
 */
ERASER_EXPORT
eraserCreateContext(E_OUT ERASER_HANDLE *param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserCreateContext\n");

    // 检查库是否已初始化
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    } else if (!AfxIsValidAddress(param1, sizeof(ERASER_HANDLE))) {
        return ERASER_ERROR_PARAM1;
    } else {
        try {
            *param1 = ERASER_INVALID_CONTEXT;  // 初始化为无效句柄
        } catch (...) {
            return ERASER_ERROR_PARAM1;
        }
    }

    eraserContextArrayAccess();

    // 查找第一个可用的上下文槽位（从高索引开始）
    for (E_UINT16 i = ERASER_MAX_CONTEXT; i >= ERASER_MIN_CONTEXT; i--) {
        if (eraserContextArray[i] == 0) {
            try {
                // 创建新的上下文对象
                eraserContextArray[i] = new CEraserContext();
            } catch (...) {
                eraserContextArray[i] = 0;
                return ERASER_ERROR_MEMORY;
            }

            try {
                // 加载库设置，如果失败则使用默认设置
                if (!loadLibrarySettings(&eraserContextArray[i]->m_lsSettings)) {
                    setLibraryDefaults(&eraserContextArray[i]->m_lsSettings);
                }

                // 重新播种伪随机数生成器
                isaacSeed();

                // 生成上下文标识
                isaacFill((E_PUINT8)&eraserContextArray[i]->m_uContextID, sizeof(E_UINT16));
                eraserContextArray[i]->m_uOwnerThreadID = ::GetCurrentThreadId();

                // 上下文句柄是数组索引和上下文ID的组合
                eraserSetContextID(*param1, eraserContextArray[i]->m_uContextID);
                eraserSetContextIndex(*param1, i);
            } catch (...) {
                ASSERT(0);
                if (AfxIsValidAddress(eraserContextArray[i], sizeof(CEraserContext))) {
                    delete eraserContextArray[i];
                    eraserContextArray[i] = 0;
                }
                return ERASER_ERROR_EXCEPTION;
            }
            return ERASER_OK;
        }
    }

    return ERASER_ERROR_CONTEXT;  // 没有可用的上下文槽位
}

/**
 * 将擦除方法枚举转换为内部方法ID
 * @param mIn 擦除方法枚举值
 * @return E_UINT8 内部方法标识符
 *
 * 功能说明：
 * - 将公共API的擦除方法枚举转换为库内部使用的方法ID
 * - 支持所有标准擦除方法的转换
 * - 对于未知方法，直接返回原值
 */
E_UINT8 convEraseMethod(ERASER_METHOD mIn)
{
	switch(mIn){
		case ERASER_METHOD_LIBRARY:
			return GUTMANN_METHOD_ID;        // 库默认方法（Gutmann）
			break;
		case ERASER_METHOD_GUTMANN:
			return GUTMANN_METHOD_ID;        // Gutmann 35遍擦除
			break;
		case ERASER_METHOD_DOD:
			return DOD_METHOD_ID;            // 美国国防部标准
			break;
		case ERASER_METHOD_DOD_E:
			return DOD_E_METHOD_ID;          // 美国国防部扩展标准
			break;
		case ERASER_METHOD_PSEUDORANDOM:
			return RANDOM_METHOD_ID;         // 伪随机数据擦除
			break;
		case ERASER_METHOD_FIRST_LAST_2KB:
			return FL2KB_METHOD_ID;          // 仅擦除首末2KB
			break;
        case ERASER_METHOD_SCHNEIER:
			return SCHNEIER_METHOD_ID;       // Schneier 7遍擦除
			break;
		default:
			return (E_UINT8)mIn;             // 未知方法，返回原值
	}
}

/**
 * 创建擦除上下文并设置自定义参数
 * @param param1 输出参数，返回创建的上下文句柄
 * @param param2 擦除方法ID
 * @param param3 擦除遍数（仅对随机方法有效）
 * @param param4 要擦除的项目标志位掩码
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 创建新的擦除上下文并设置指定的擦除方法和参数
 * - 支持所有内置的擦除方法
 * - 可以设置自定义的擦除遍数（仅对伪随机方法有效）
 * - 可以指定要擦除的具体项目（文件内容、文件名、簇尾部等）
 */
ERASER_EXPORT
eraserCreateContextEx(E_OUT ERASER_HANDLE *param1, E_IN E_UINT8 param2, E_IN E_UINT16 param3, E_IN E_UINT8 param4)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserCreateContextEx\n");
    LONG lRetStatus = ERASER_OK;

    // 参数验证
	if (param2==0) {
        return ERASER_ERROR_PARAM2;  // 方法ID不能为0
    } else if (param3 > PASSES_MAX || param3 < 1) {
        return ERASER_ERROR_PARAM3;  // 擦除遍数必须在有效范围内
    }

    // 首先创建基础上下文（执行基本的完整性检查）
    ERASER_RESULT result = eraserCreateContext(param1);
    if (eraserError(result)) {
        return result;
    } else {
        try {
            CEraserContext *context = 0;
            if (eraserOK(contextToAddress(*param1, &context))) {
                eraserContextAccess(context);

                // 设置要擦除的项目标志
                if (param4) {
                    context->m_lsSettings.m_uItems = param4;
                }

                // 根据方法ID设置相应的擦除方法
                switch (param2) {
                case GUTMANN_METHOD_ID:
                    // Gutmann 35遍擦除方法
                    context->m_lsSettings.m_nFileMethodID = GUTMANN_METHOD_ID;
                    context->m_lsSettings.m_nUDSMethodID  = GUTMANN_METHOD_ID;
                    break;
                case DOD_METHOD_ID:
                    // 美国国防部标准擦除方法
                    context->m_lsSettings.m_nFileMethodID = DOD_METHOD_ID;
                    context->m_lsSettings.m_nUDSMethodID  = DOD_METHOD_ID;
                    break;
                case DOD_E_METHOD_ID:
                    // 美国国防部扩展擦除方法
                    context->m_lsSettings.m_nFileMethodID = DOD_E_METHOD_ID;
                    context->m_lsSettings.m_nUDSMethodID  = DOD_E_METHOD_ID;
                    break;
                case RANDOM_METHOD_ID:
                    // 伪随机数据擦除方法
                    context->m_lsSettings.m_nFileMethodID = RANDOM_METHOD_ID;
                    context->m_lsSettings.m_nUDSMethodID  = RANDOM_METHOD_ID;
                    context->m_lsSettings.m_nFileRandom   = param3;  // 文件擦除遍数
                    context->m_lsSettings.m_nUDSRandom    = param3;  // 未使用空间擦除遍数
                    break;
				case FL2KB_METHOD_ID:
                    // 仅擦除文件首末2KB的方法
                    context->m_lsSettings.m_nFileMethodID = FL2KB_METHOD_ID;
                    context->m_lsSettings.m_nUDSMethodID  = FL2KB_METHOD_ID;
					break;
				case SCHNEIER_METHOD_ID:
                    // Schneier 7遍擦除方法
                    context->m_lsSettings.m_nFileMethodID = SCHNEIER_METHOD_ID;
                    context->m_lsSettings.m_nUDSMethodID  = SCHNEIER_METHOD_ID;
					break;
                default:
                    // 处理自定义擦除方法
					{
						LibrarySettings lsTemp;
						BOOL bExist = FALSE;

                        // 加载库设置以检查自定义方法是否存在
						if (loadLibrarySettings(&lsTemp))
						{
                            // 遍历所有自定义方法，查找匹配的方法ID
							for(int i = 0; i < lsTemp.m_nCMethods; i++)
								if (lsTemp.m_lpCMethods->m_nMethodID == param2) bExist = TRUE;

                            // 如果找到匹配的自定义方法，设置为当前方法
							if (bExist) {
								context->m_lsSettings.m_nFileMethodID = param2;
								context->m_lsSettings.m_nUDSMethodID  = param2;
							}
							else {
                                // 自定义方法不存在，返回参数错误
								lRetStatus = ERASER_ERROR_PARAM2;
							}
						}
						else{
                            // 无法加载库设置，返回参数错误
							lRetStatus = ERASER_ERROR_PARAM2;
						}
					}
                }

                return lRetStatus;
            }
            return ERASER_ERROR_CONTEXT;

        } catch (...) {
            ASSERT(0);
            try {
                // 发生异常时清理已创建的上下文
                eraserDestroyContext(*param1);
            } catch (...) {
                // 忽略清理过程中的异常
            }
            return ERASER_ERROR_EXCEPTION;
        }
    }
}

/**
 * 销毁擦除上下文
 * @param param1 要销毁的上下文句柄
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 停止正在运行的擦除操作
 * - 从全局上下文数组中移除上下文
 * - 释放上下文占用的内存资源
 * - 确保线程安全的清理过程
 */
ERASER_EXPORT
eraserDestroyContext(E_IN ERASER_HANDLE param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserDestroyContext\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else {
        try {
            // 确保停止正在运行的擦除操作
            VERIFY(eraserOK(eraserStop(param1)));

            // 从全局上下文数组中移除
            eraserContextArrayAccess();
            eraserContextArray[eraserContextIndex(param1)] = 0;
            eraserContextArrayRelease();

            // 锁定上下文并释放内存
            eraserContextLock(context);
            delete context;
            context = 0;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

/**
 * 检查上下文句柄的有效性
 * @param param1 要检查的上下文句柄
 * @return ERASER_RESULT ERASER_OK表示有效，其他值表示无效
 *
 * 功能说明：
 * - 验证上下文句柄是否有效且可访问
 * - 检查句柄是否指向有效的上下文对象
 * - 验证线程所有权和上下文ID匹配
 */
ERASER_EXPORT
eraserIsValidContext(E_IN ERASER_HANDLE param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserIsValidContext\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else {
        return ERASER_OK;  // 上下文有效
    }
}
/**
 * 设置错误处理回调函数
 * @param param1 上下文句柄
 * @param pfn 错误处理回调函数指针
 * @param fnParam 传递给回调函数的用户参数
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 设置自定义的错误处理回调函数
 * - 当擦除过程中发生错误时会调用此回调
 * - 允许应用程序自定义错误处理逻辑
 * - 不能在擦除操作运行时设置
 */
ERASER_EXPORT
eraserSetErrorHandler(E_IN ERASER_HANDLE param1, EraserErrorHandler pfn, void* fnParam)
{
	CEraserContext *context = 0;
	if (eraserError(contextToAddress(param1, &context)))
	{
		return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
	}
	else if (eraserInternalIsRunning(context))
	{
		return ERASER_ERROR_RUNNING; // 操作正在运行中
	}
	else
	{
		try
		{
			eraserContextAccess(context);
			context->m_pfnErrorHandler = pfn;        // 设置错误处理函数
			context->m_pErrorHandlerParam = fnParam; // 设置用户参数
		}
		catch (...)
		{
			ASSERT(0);
			return ERASER_ERROR_EXCEPTION;
		}
		return ERASER_OK;
	}

}

// ============================================================================
// 数据类型管理函数
// ============================================================================

/**
 * 设置上下文的数据类型
 * @param param1 上下文句柄
 * @param param2 数据类型（ERASER_DATA_DRIVES 或 ERASER_DATA_FILES）
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 设置上下文要处理的数据类型（驱动器或文件）
 * - 只能在添加数据项之前设置数据类型
 * - 不能在擦除操作运行时更改数据类型
 */
ERASER_EXPORT
eraserSetDataType(E_IN ERASER_HANDLE param1, E_IN ERASER_DATA_TYPE param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserSetDataType\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!eraserIsValidDataType(param2)) {
        return ERASER_ERROR_PARAM2;  // 无效的数据类型
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING; // 操作正在运行中
    } else {
        try {
            eraserContextAccess(context);
            if (context->m_saData.GetSize() == 0) {
                // 只有在没有添加数据项时才能设置数据类型
                context->m_edtDataType = param2;
            } else {
                // 添加数据项后不能更改数据类型
                return ERASER_ERROR_DENIED;
            }
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

/**
 * 获取上下文的数据类型
 * @param param1 上下文句柄
 * @param param2 输出参数，返回数据类型
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 查询上下文当前设置的数据类型
 * - 返回 ERASER_DATA_DRIVES（驱动器）或 ERASER_DATA_FILES（文件）
 */
ERASER_EXPORT
eraserGetDataType(E_IN ERASER_HANDLE param1, E_OUT ERASER_DATA_TYPE *param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserGetDataType\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(ERASER_DATA_TYPE))) {
        return ERASER_ERROR_PARAM2;  // 无效的输出参数地址
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_edtDataType;  // 返回当前数据类型
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}


// ============================================================================
// 数据项管理函数
// ============================================================================

/**
 * 向上下文数据数组添加项目
 * @param param1 上下文句柄
 * @param param2 要添加的数据项（文件路径或驱动器路径）
 * @param param3 数据长度
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 添加要擦除的文件或驱动器到上下文
 * - 要求使用完全限定的路径名
 * - 驱动器必须以"X:\\"格式给出
 * - 支持UNC路径（\\server\share\...）
 * - 不能在擦除操作运行时添加项目
 */
ERASER_EXPORT
eraserAddItem(E_IN ERASER_HANDLE param1, E_IN LPVOID param2, E_IN E_UINT16 param3)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserAddItem\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (param3 < _MAX_DRIVE || param3 > _MAX_PATH) {
        return ERASER_ERROR_PARAM3;  // 路径长度无效
    } else if (!AfxIsValidString((LPCTSTR)param2, param3)) {
        return ERASER_ERROR_PARAM2;  // 无效的字符串参数
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING; // 操作正在运行中
    } else {
        try {
            LPCTSTR szItem = (LPCTSTR)param2;

            // 验证路径格式：必须是完全限定的路径名
            // 本地路径：驱动器必须以"X:\\"格式给出
            // UNC路径：必须以"\\\\"开头且包含服务器和共享名
            if (!(isalpha(szItem[0]) && szItem[1] == ':' && szItem[2] == '\\') &&
            	!(_tcsncmp(_T("\\\\"), szItem, 2) == 0 && _tcschr(szItem + 2, '\\') != NULL)) {
                return ERASER_ERROR_PARAM2;  // 路径格式无效
            }

            eraserContextAccess(context);

            // 根据数据类型检查路径长度限制
            if ((context->m_edtDataType == ERASER_DATA_FILES  && _tcslen(szItem) > _MAX_PATH) ||
                (context->m_edtDataType == ERASER_DATA_DRIVES && _tcslen(szItem) > _MAX_DRIVE)) {
                return ERASER_ERROR_PARAM2;  // 路径长度超出限制
            } else {
                // 将项目添加到数据数组
                context->m_saData.Add(szItem);
            }
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

/**
 * 设置完成后的操作
 * @param param1 上下文句柄
 * @param action ExitWindowsEx函数的标志（如关机、重启等）
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 设置擦除操作完成后要执行的系统操作
 * - 支持关机、重启、注销等操作
 * - 使用Windows API ExitWindowsEx的标志值
 * - 不能在擦除操作运行时设置
 */
ERASER_EXPORT
eraserSetFinishAction(E_IN ERASER_HANDLE param1, E_IN DWORD action)
{
	CEraserContext *context = 0;
	if (eraserError(contextToAddress(param1, &context))) {
		return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
	} else if (eraserInternalIsRunning(context)) {
		return ERASER_ERROR_RUNNING; // 操作正在运行中
	} else {
		try {
			eraserContextAccess(context);
			context->m_dwFinishAction = action;  // 设置完成后操作
		} catch (...) {
			ASSERT(0);
			return ERASER_ERROR_EXCEPTION;
		}
		return ERASER_OK;
	}

}
/**
 * 清空上下文数据数组
 * @param param1 上下文句柄
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 清除上下文中所有已添加的数据项
 * - 清空后可以重新添加新的文件或驱动器
 * - 不能在擦除操作运行时清空
 */
ERASER_EXPORT
eraserClearItems(E_IN ERASER_HANDLE param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserClearItems\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING; // 操作正在运行中
    } else {
        try {
            eraserContextAccess(context);
            context->m_saData.RemoveAll();  // 清空数据数组
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}


// ============================================================================
// 通知管理函数
// ============================================================================

/**
 * 设置接收进度通知的窗口句柄
 * @param param1 上下文句柄
 * @param param2 窗口句柄
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 设置接收擦除进度通知的目标窗口
 * - 擦除过程中会向此窗口发送进度消息
 * - 窗口必须是有效的窗口句柄
 * - 用于实现进度监控和用户界面更新
 */
ERASER_EXPORT
eraserSetWindow(E_IN ERASER_HANDLE param1, E_IN HWND param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserSetWindow\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!IsWindow(param2)) {
        return ERASER_ERROR_PARAM2;  // 无效的窗口句柄
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else {
        try {
            eraserContextAccess(context);
            context->m_hwndWindow = param2;  // 设置通知窗口
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserGetWindow(E_IN ERASER_HANDLE param1, E_OUT HWND* param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserGetWindow\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(HWND))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_hwndWindow;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserSetWindowMessage(E_IN ERASER_HANDLE param1, E_IN E_UINT32 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserSetWindowMessage\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else {
        try {
            eraserContextAccess(context);
            context->m_uWindowMessage = param2;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserGetWindowMessage(E_IN ERASER_HANDLE param1, E_OUT E_PUINT32 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserGetWindowMessage\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT32))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_uWindowMessage;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}


// ============================================================================
// 统计信息函数
// ============================================================================

/**
 * 获取已擦除的区域大小统计
 * @param param1 上下文句柄
 * @param param2 输出参数，返回已擦除的字节数
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 返回实际被擦除的数据区域大小
 * - 包括文件内容和簇尾部空间
 * - 用于统计擦除操作的效果
 * - 不能在操作运行时调用
 */
ERASER_EXPORT
eraserStatGetArea(E_IN ERASER_HANDLE param1, E_OUT E_PUINT64 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserStatGetArea\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT64))) {
        return ERASER_ERROR_PARAM2;  // 无效的输出参数地址
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING; // 操作正在运行中
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_uStatErasedArea;  // 返回已擦除区域大小
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserStatGetTips(E_IN ERASER_HANDLE param1, E_OUT E_PUINT64 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserStatGetTips\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT64))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_uStatTips;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserStatGetWiped(E_IN ERASER_HANDLE param1, E_OUT E_PUINT64 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserStatGetWiped\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT64))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_uStatWiped;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserStatGetTime(E_IN ERASER_HANDLE param1, E_OUT E_PUINT32 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserStatGetTime\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT32))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_uStatTime;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}


// Display
//
ERASER_EXPORT
eraserDispFlags(E_IN ERASER_HANDLE param1, E_OUT E_PUINT8 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserDispFlags\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT8))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING;
    } else {
        try {
            eraserContextAccess(context);
            // low-order byte
            *param2 = (E_UINT8)context->m_uProgressFlags;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}


// ============================================================================
// 进度信息函数
// ============================================================================

/**
 * 获取预估剩余时间
 * @param param1 上下文句柄
 * @param param2 输出参数，返回预估剩余时间（毫秒）
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 返回擦除操作预估的剩余完成时间
 * - 基于当前进度和已用时间计算
 * - 用于显示进度条和时间估算
 * - 只能在操作运行时调用
 */
ERASER_EXPORT
eraserProgGetTimeLeft(E_IN ERASER_HANDLE param1, E_OUT E_PUINT32 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserProgGetTimeLeft\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT32))) {
        return ERASER_ERROR_PARAM2;  // 无效的输出参数地址
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING; // 操作未运行
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_uProgressTimeLeft;  // 返回预估剩余时间
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
********************(E_IN ERASER_HANDLE param1, E_OUT E_PUINT8 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("********************\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT8))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = min(context->m_uProgressPercent, (E_UINT8)100);
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserProgGetTotalPercent(E_IN ERASER_HANDLE param1, E_OUT E_PUINT8 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserProgGetTotalPercent\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT8))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = min(context->m_uProgressTotalPercent, (E_UINT8)100);
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserProgGetCurrentPass(E_IN ERASER_HANDLE param1, E_OUT E_PUINT16 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserProgGetCurrentPass\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT16))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_uProgressCurrentPass;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserProgGetPasses(E_IN ERASER_HANDLE param1, E_OUT E_PUINT16 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserProgGetPasses\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT16))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = context->m_uProgressPasses;
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserProgGetMessage(E_IN ERASER_HANDLE param1, E_OUT LPVOID param2, E_INOUT E_PUINT16 param3)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserProgGetMessage\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param3, sizeof(E_UINT16))) {
        return ERASER_ERROR_PARAM3;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING;
    } else {
        try {
            eraserContextAccess(context);
            LPTSTR pszError = (LPTSTR)param2;
            if (pszError == 0) {
                *param3 = (E_UINT16)(context->m_strProgressMessage.GetLength() + 1);
                return ERASER_OK;
            } else if (*param3 < 1) {
                return ERASER_ERROR_PARAM3;
            } else if (!AfxIsValidAddress((LPCTSTR)pszError, *param3)) {
                return ERASER_ERROR_PARAM2;
            }
            ZeroMemory(pszError, *param3);
            lstrcpyn(pszError, (LPCTSTR)context->m_strProgressMessage, *param3);
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserProgGetCurrentDataString(E_IN ERASER_HANDLE param1, E_OUT LPVOID param2, E_INOUT E_PUINT16 param3)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceProgress("eraserProgGetCurrentDataString\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param3, sizeof(E_UINT16))) {
        return ERASER_ERROR_PARAM3;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING;
    } else {
        try {
            eraserContextAccess(context);
            LPTSTR pszError = (LPTSTR)param2;
            if (pszError == 0) {
                *param3 = (E_UINT16)(context->m_strData.GetLength() + 1);
                return ERASER_OK;
            } else if (*param3 < 1) {
                return ERASER_ERROR_PARAM3;
            } else if (!AfxIsValidAddress((LPCTSTR)pszError, *param3)) {
                return ERASER_ERROR_PARAM2;
            }
            ZeroMemory(pszError, *param3);
            lstrcpyn(pszError, (LPCTSTR)context->m_strData, *param3);
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}


// ============================================================================
// 操作控制函数
// ============================================================================

/**
 * 在新线程中开始擦除操作（异步）
 * @param param1 上下文句柄
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 创建新的工作线程来执行擦除操作
 * - 函数立即返回，不等待擦除完成
 * - 可以通过进度查询函数监控擦除进度
 * - 使用事件对象进行线程同步
 */
ERASER_EXPORT
eraserStart(E_IN ERASER_HANDLE param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserStart\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;  // 已经在运行中
    } else {
        try {
            eraserContextAccess(context);
            // 预设事件标志
            context->m_evDone.SetEvent();          // 设置完成事件
            context->m_evKillThread.ResetEvent();  // 重置终止事件

            // 创建工作线程
            context->m_pwtThread = AfxBeginThread(eraserThread, (LPVOID)context);
            if (context->m_pwtThread == NULL) {
                return ERASER_ERROR_THREAD;  // 线程创建失败
            }

            // 启动操作
            eraserContextRelease();
            context->m_evStart.SetEvent();  // 通知线程开始工作
        } catch (...) {
            ASSERT(0);
            eraserStop(param1);  // 出错时停止操作
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

/**
 * 开始擦除操作（同步）
 * @param param1 上下文句柄
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 在当前线程中执行擦除操作
 * - 函数会阻塞直到擦除操作完成
 * - 适用于不需要进度监控的简单场景
 * - 直接调用擦除线程函数，不创建新线程
 */
ERASER_EXPORT
eraserStartSync(E_IN ERASER_HANDLE param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserStartSync\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;  // 已经在运行中
    } else {
        try {
            eraserContextAccess(context);
            // 预设事件标志
            context->m_evDone.SetEvent();          // 设置完成事件
            context->m_evKillThread.ResetEvent();  // 重置终止事件
            context->m_evStart.SetEvent();         // 设置启动事件
            eraserContextRelease();

            // 直接在当前线程中开始擦除（同步执行）
            if (eraserThread((LPVOID)context) == EXIT_SUCCESS) {
                return ERASER_OK;
            } else {
                return ERASER_ERROR;
            }
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
    }
}


ERASER_EXPORT
eraserStop(E_IN ERASER_HANDLE param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserStop\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else {
        try {
            // 设置终止标志，通知工作线程停止
            context->m_evKillThread.SetEvent();
            context->m_evStart.SetEvent();

            // 如果在测试模式下，重新启用执行以便线程能够检查终止标志
            eraserContextAccess(context);
            if (context->m_uTestMode) {
                context->m_evTestContinue.SetEvent();
            }
            eraserContextRelease();

            // 等待线程自然结束（最多2分钟）
            // 这应该足够任何线程完成清理工作
            if (WaitForSingleObject(context->m_evThreadKilled, 120000) != WAIT_OBJECT_0) {
                // 如果线程仍然活跃，强制终止它
                eraserContextRelock();
                if (AfxIsValidAddress(context->m_pwtThread, sizeof(CWinThread))) {
                    E_UINT32 uStatus = 0;
                    if (::GetExitCodeThread(context->m_pwtThread->m_hThread, &uStatus) &&
                        uStatus == STILL_ACTIVE) {
                        // 强制终止线程（这是最后的手段）
                        VERIFY(::TerminateThread(context->m_pwtThread->m_hThread, (E_UINT32)ERASER_ERROR));
                    }
                }
                context->m_evThreadKilled.SetEvent();
            }
            eraserContextRelock();
            context->m_pwtThread = 0;  // 清空线程指针
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

/**
 * 检查任务是否正在运行
 * @param param1 上下文句柄
 * @param param2 输出参数，返回运行状态（非0表示正在运行）
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 检查指定上下文的擦除操作是否正在进行
 * - 用于监控异步操作的状态
 */
ERASER_EXPORT
eraserIsRunning(E_IN ERASER_HANDLE param1, E_OUT E_PUINT8 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserIsRunning\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT8))) {
        return ERASER_ERROR_PARAM2;  // 无效的输出参数地址
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else {
        try {
            eraserContextAccess(context);
            *param2 = 0;  // 默认为未运行
            if (eraserInternalIsRunning(context)) {
                *param2 = 1;  // 正在运行
            }
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

// ============================================================================
// 结果查询函数
// ============================================================================

/**
 * 检查任务是否被终止
 * @param param1 上下文句柄
 * @param param2 输出参数，返回终止状态（非0表示被终止）
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 检查擦除操作是否被用户或系统终止
 * - 只能在操作完成后调用（不能在运行时调用）
 * - 用于区分正常完成和异常终止
 */
ERASER_EXPORT
eraserTerminated(E_IN ERASER_HANDLE param1, E_OUT E_PUINT8 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserTerminated\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT8))) {
        return ERASER_ERROR_PARAM2;  // 无效的输出参数地址
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING; // 操作仍在运行中
    } else {
        try {
            eraserContextAccess(context);
            *param2 = 0;  // 默认为未终止
            if (eraserInternalTerminated(context)) {
                *param2 = 1;  // 操作被终止
            }
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserCompleted(E_IN ERASER_HANDLE param1, E_OUT E_PUINT8 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserCompleted\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT8))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = 0;
            if (eraserInternalCompleted(context)) {
                *param2 = 1;
            }
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserFailed(E_IN ERASER_HANDLE param1, E_OUT E_PUINT8 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserFailed\n");
    ERASER_RESULT result = eraserCompleted(param1, param2);

    if (eraserOK(result)) {
        try {
            if (*param2) {
                *param2 = 0;
            } else {
                *param2 = 1;
            }
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
    }

    return result;
}

ERASER_EXPORT
eraserErrorStringCount(E_IN ERASER_HANDLE param1, E_OUT E_PUINT16 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserErrorStringCount\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT16))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = (E_UINT16)context->m_saError.GetSize();
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserErrorString(E_IN ERASER_HANDLE param1, E_IN E_UINT16 param2, E_OUT LPVOID param3, E_INOUT E_PUINT16 param4)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserErrorString\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param4, sizeof(E_UINT16))) {
        return ERASER_ERROR_PARAM4;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    } else {
        try {
            eraserContextAccess(context);
            if (context->m_saError.GetSize() <= param2) {
                return ERASER_ERROR_PARAM2;
            }
            LPTSTR pszError = (LPTSTR)param3;
            if (pszError == 0) {
                *param4 = (E_UINT16)(context->m_saError[param2].GetLength() + 1);
                return ERASER_OK;
            } else if (*param4 < 1) {
                return ERASER_ERROR_PARAM4;
            } else if (!AfxIsValidAddress((LPCTSTR)pszError, *param4)) {
                return ERASER_ERROR_PARAM3;
            }
            ZeroMemory(pszError, *param4);
            lstrcpyn(pszError, (LPCTSTR)context->m_saError[param2], *param4);
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserFailedCount(E_IN ERASER_HANDLE param1, E_OUT E_PUINT32 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserFailedCount\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param2, sizeof(E_UINT32))) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    } else {
        try {
            eraserContextAccess(context);
            *param2 = (E_UINT32)context->m_saFailed.GetSize();
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

ERASER_EXPORT
eraserFailedString(E_IN ERASER_HANDLE param1, E_IN E_UINT32 param2, E_OUT LPVOID param3, E_INOUT E_PUINT16 param4)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceQuery("eraserFailedString\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!AfxIsValidAddress(param4, sizeof(E_UINT16))) {
        return ERASER_ERROR_PARAM4;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    } else {
        try {
            eraserContextAccess(context);
            if ((E_UINT32)context->m_saFailed.GetSize() <= param2) {
                return ERASER_ERROR_PARAM2;
            }
            LPTSTR pszError = (LPTSTR)param3;
            if (pszError == 0) {
                *param4 = (E_UINT16)(context->m_saFailed[param2].GetLength() + 1);
                return ERASER_OK;
            } else if (*param4 < 1) {
                return ERASER_ERROR_PARAM4;
            } else if (!AfxIsValidAddress((LPCTSTR)pszError, *param4)) {
                return ERASER_ERROR_PARAM3;
            }
            ZeroMemory(pszError, *param4);
            lstrcpyn(pszError, (LPCTSTR)context->m_saFailed[param2], *param4);
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}


// Display report
//
ERASER_EXPORT
eraserShowReport(E_IN ERASER_HANDLE param1, E_IN HWND param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserShowReport\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (!IsWindow(param2)) {
        return ERASER_ERROR_PARAM2;
    } else if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING;
    }

    ERASER_RESULT result = ERASER_OK;
    CWnd wndParent;
    wndParent.Attach(param2);

    try {
        CStringArray straFailures;
        CString strTemp;
        CString strUnit;
        E_UINT64 uTemp;
        INT_PTR uIndex, uSize;
        double dTime;
        CReportDialog rd;

        // completion
        if (eraserInternalCompleted(context)) {
            rd.m_strCompletion = "Task completed.";
        } else if (eraserInternalTerminated(context)) {
            rd.m_strCompletion = "Task terminated by user.";
        } else {
            if (context->m_saError.GetSize() > 0) {
                rd.m_strCompletion = "Task was not completed.";
            } else {
                rd.m_strCompletion = "Task completed. All data could not be erased.";
            }
        }

        #define reportMaxByteValue    (10 * 1024)
        #define reportMaxkBValue    (1000 * 1024)
        
        #define divideByK(value) \
            (value) = (((value) + 512) / 1024)

        #define setValueAndUnit(value) \
            do { \
                uTemp = (value); \
                if (uTemp > reportMaxByteValue) { \
                    divideByK(uTemp); \
                    if (uTemp > reportMaxkBValue) { \
                        divideByK(uTemp); \
                        strUnit = "MB"; \
                    } else { \
                        strUnit = "kB"; \
                    } \
                } else { \
                    if ((value) != 1) { \
                        strUnit = "bytes"; \
                    } else { \
                        strUnit = "byte"; \
                    } \
                } \
            } while (0)

        // information header
        rd.m_strStatistics = _T("Statistics:\r\n");
        // erased area
        setValueAndUnit(context->m_uStatErasedArea);
        strTemp.Format(_T("    Erased area\t\t\t=  %I64u %s\r\n"), uTemp, strUnit);
        rd.m_strStatistics += strTemp;
        // cluster tips
        setValueAndUnit(context->m_uStatTips);
        strTemp.Format(_T("    Cluster tips\t\t\t=  %I64u %s\r\n"), uTemp, strUnit);
        rd.m_strStatistics += strTemp;
        // written
        setValueAndUnit(context->m_uStatWiped);
        strTemp.Format(_T("\r\n    Data written\t\t\t=  %I64u %s\r\n"), uTemp, strUnit);
        rd.m_strStatistics += strTemp;
        // time
        dTime = (double)context->m_uStatTime / 1000.0f;
        strTemp.Format(_T("    Write time\t\t\t=  %.2f %s"), dTime, _T("s"));
        rd.m_strStatistics += strTemp;
        // speed
        if (dTime > 0.0) {
            strTemp.Format(_T("\r\n    Write speed\t\t\t=  %I64u %s"), (E_UINT64)
                ((((E_INT64)context->m_uStatWiped / dTime) + 512.0f) / 1024.0f), _T("kB/s"));
            rd.m_strStatistics += strTemp;
        }

        uSize = context->m_saError.GetSize();
        for (uIndex = 0; uIndex < uSize; uIndex++) {
            strTemp.Format(_T("Error: %s"), context->m_saError[uIndex]);
            straFailures.Add(strTemp);
        }

        uSize = context->m_saFailed.GetSize();
        for (uIndex = 0; uIndex < uSize; uIndex++) {
            strTemp.Format(_T("Failed: %s"), context->m_saFailed[uIndex]);
            straFailures.Add(strTemp);
        }

        rd.m_pstraErrorArray = &straFailures;

        rd.DoModal();
    } catch (...) {
        ASSERT(0);
        result = ERASER_ERROR_EXCEPTION;
    }

    wndParent.Detach();
    return result;
}


// Display library options
//
ERASER_EXPORT
eraserShowOptions(E_IN HWND param1, E_IN ERASER_OPTIONS_PAGE param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserShowOptions\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    } else if (!IsWindow(param1)) {
        return ERASER_ERROR_PARAM1;
    }

    E_UINT16 uActive;
    if (param2 == ERASER_PAGE_DRIVE) {
        uActive = 1;
    } else if (param2 == ERASER_PAGE_FILES) {
        uActive = 0;
    } else {
        return ERASER_ERROR_PARAM2;
    }

    ERASER_RESULT result = ERASER_OK;

    CWnd wndParent;
    wndParent.Attach(param1);

    try {
        
		COptionsDlg dlg(&wndParent);
        dlg.SetActivePage(uActive);

        if (!loadLibrarySettings(&dlg.m_lsSettings))
            setLibraryDefaults(&dlg.m_lsSettings);

        AFX_MANAGE_STATE(AfxGetStaticModuleState( ));
		if (dlg.DoModal() == IDOK)
            saveLibrarySettings(&dlg.m_lsSettings);
    } catch (...) {
        ASSERT(0);
        result = ERASER_ERROR_EXCEPTION;
    }

    wndParent.Detach();
    return result;
}


// ============================================================================
// 文件/目录删除函数
// ============================================================================

/**
 * 安全删除文件
 * @param param1 文件路径
 * @param param2 路径长度
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 安全地删除文件，防止文件名恢复
 * - 在NT系统上首先覆写文件名，然后删除文件
 * - 重置文件属性以确保可以删除
 * - 这是一个独立的工具函数，不需要创建擦除上下文
 */
ERASER_EXPORT
eraserRemoveFile(E_IN LPVOID param1, E_IN E_UINT16 param2)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserRemoveFile\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    LPCTSTR pszFile = (LPCTSTR)param1;
    if (!AfxIsValidString(pszFile, param2)) {
        return ERASER_ERROR_PARAM1;  // 无效的文件路径
    }

    // 重置文件属性，确保文件可以被删除
    SetFileAttributes(pszFile, FILE_ATTRIBUTE_NORMAL);
	SetFileAttributes(pszFile, FILE_ATTRIBUTE_NOT_CONTENT_INDEXED);

    if (isWindowsNT) {
        // 在Windows NT系列系统上使用安全删除
        TCHAR szLastFileName[MAX_PATH + 1];

        // 覆写文件名以防止恢复
        overwriteFileName(pszFile, szLastFileName);
		void makeWindowsSystemFile(LPTSTR filename);
		makeWindowsSystemFile(szLastFileName);  // 设置为系统文件属性
        return truthToResult(DeleteFile(szLastFileName));
    }

    // 在非NT系统上直接删除
    return truthToResult(DeleteFile(pszFile));
}

/**
 * 删除文件夹
 * @param param1 文件夹路径
 * @param param2 路径长度
 * @param param3 删除选项（ERASER_REMOVE_FOLDERONLY 或 ERASER_REMOVE_RECURSIVELY）
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 安全删除文件夹及其内容
 * - 支持递归删除或仅删除空文件夹
 * - 在NT系统上会覆写文件夹名称以防止恢复
 * - 使用安全删除方法处理文件夹中的所有文件
 */
ERASER_EXPORT
eraserRemoveFolder(E_IN LPVOID param1, E_IN E_UINT16 param2, E_IN E_UINT8 param3)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserRemoveFolder\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    LPCTSTR pszFolder = (LPCTSTR)param1;
    if (!AfxIsValidString(pszFolder, param2)) {
        return ERASER_ERROR_PARAM1;  // 无效的文件夹路径
    }

    // 重置文件夹属性，确保可以删除
    SetFileAttributes(pszFolder, FILE_ATTRIBUTE_NORMAL);

    // 递归删除所有子文件夹和文件
    if (param3 != ERASER_REMOVE_FOLDERONLY) {
        emptyFolder(pszFolder);  // 清空文件夹内容
    }

    if (isWindowsNT) {
        // 在NT系统上使用安全删除
        if (!isFolderEmpty(pszFolder)) {
            return ERASER_ERROR;  // 文件夹不为空
        }

        CString strFolder(pszFolder);
        TCHAR   szLastFileName[MAX_PATH + 1];

        // 移除末尾的反斜杠（如果有的话）
        if (strFolder[strFolder.GetLength() - 1] == '\\') {
            strFolder = strFolder.Left(strFolder.GetLength() - 1);
        }

        // 覆写文件夹名称以防止恢复
        overwriteFileName((LPCTSTR)strFolder, szLastFileName);
        return truthToResult(RemoveDirectory(szLastFileName));
    }

    // 在非NT系统上直接删除
    return truthToResult(RemoveDirectory(pszFolder));
}


// ============================================================================
// 辅助工具函数
// ============================================================================

/**
 * 获取磁盘可用空间
 * @param param1 驱动器路径（如"C:\\"）
 * @param param2 路径长度
 * @param param3 输出参数，返回可用空间字节数
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 获取指定驱动器的可用磁盘空间
 * - 支持本地驱动器和网络驱动器
 * - 用于计算未使用空间擦除的工作量
 * - 优先使用GetDiskFreeSpaceEx API（支持大容量磁盘）
 */
ERASER_EXPORT
eraserGetFreeDiskSpace(E_IN LPVOID param1, E_IN E_UINT16 param2, E_OUT E_PUINT64 param3)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserGetFreeDiskSpace\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    LPCTSTR pszDrive = (LPCTSTR)param1;
    if (!AfxIsValidString(pszDrive, param2)) {
        return ERASER_ERROR_PARAM1;  // 无效的驱动器路径
    } else if (!AfxIsValidAddress(param3, sizeof(E_UINT64))) {
        return ERASER_ERROR_PARAM3;  // 无效的输出参数地址
    } else {
        try {
            *param3 = 0;  // 初始化输出参数
        } catch (...) {
            return ERASER_ERROR_PARAM3;
        }
    }

    ERASER_RESULT result = ERASER_ERROR;
    HINSTANCE hInst = AfxLoadLibrary(ERASER_MODULENAME_KERNEL);

    if (hInst != NULL) {
        ULARGE_INTEGER FreeBytesAvailableToCaller;
        ULARGE_INTEGER TotalNumberOfBytes;
        ULARGE_INTEGER TotalNumberOfFreeBytes;

        GETDISKFREESPACEEX pGetDiskFreeSpaceEx;

        pGetDiskFreeSpaceEx =
            (GETDISKFREESPACEEX)(GetProcAddress(hInst, ERASER_FUNCTIONNAME_GETDISKFREESPACEEX));

        if (pGetDiskFreeSpaceEx) {
            try {
                if (pGetDiskFreeSpaceEx(pszDrive, &FreeBytesAvailableToCaller,
                        &TotalNumberOfBytes, &TotalNumberOfFreeBytes)) {
                    *param3 = TotalNumberOfFreeBytes.QuadPart;
                    result = ERASER_OK;
                }
            } catch (...) {
                result = ERASER_ERROR_EXCEPTION;
            }
        }

        AfxFreeLibrary(hInst);
    }

    if (eraserError(result)) {
        E_UINT32 dwSecPerClus;
        E_UINT32 dwBytPerSec;
        E_UINT32 dwFreeClus;
        E_UINT32 dwClus;

        try {
            if (GetDiskFreeSpace(pszDrive, &dwSecPerClus, &dwBytPerSec,
                    &dwFreeClus, &dwClus)) {

                *param3 = UInt32x32To64(dwFreeClus, dwSecPerClus * dwBytPerSec);
                result = ERASER_OK;
            }
        } catch (...) {
            result = ERASER_ERROR_EXCEPTION;
        }
    }

    return result;
}

/**
 * 获取磁盘簇大小
 * @param param1 驱动器路径（如"C:\\"）
 * @param param2 路径长度
 * @param param3 输出参数，返回簇大小（字节）
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 获取指定驱动器的文件系统簇大小
 * - 簇是文件系统分配的最小单位
 * - 用于计算簇尾部（slack space）的大小
 * - 对于安全擦除簇尾部空间很重要
 */
ERASER_EXPORT
eraserGetClusterSize(E_IN LPVOID param1, E_IN E_UINT16 param2, E_OUT E_PUINT32 param3)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserGetClusterSize\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    LPCTSTR pszDrive = (LPCTSTR)param1;
    if (!AfxIsValidString(pszDrive, param2)) {
        return ERASER_ERROR_PARAM1;  // 无效的驱动器路径
    } else if (!AfxIsValidAddress(param3, sizeof(E_UINT64))) {
        return ERASER_ERROR_PARAM3;  // 无效的输出参数地址
    } else {
        try {
            *param3 = 0;  // 初始化输出参数
        } catch (...) {
            return ERASER_ERROR_PARAM3;
        }
    }

    ERASER_RESULT result = ERASER_ERROR;

    try {
        result = truthToResult(getClusterSize(pszDrive, *param3));
    } catch (...) {
        result = ERASER_ERROR_EXCEPTION;
    }

    return result;
}

/**
 * 启用测试模式
 * @param param1 上下文句柄
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 启用测试模式，擦除过程会在每遍完成后暂停
 * - 允许用户检查每遍擦除的结果
 * - 用于验证擦除算法的有效性
 * - 不能在擦除操作运行时启用
 */
ERASER_EXPORT
eraserTestEnable(E_IN ERASER_HANDLE param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserTestEnable\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else if (eraserInternalIsRunning(context)) {
        return ERASER_ERROR_RUNNING; // 操作正在运行中
    } else {
        try {
            eraserContextAccess(context);
            context->m_uTestMode = 1;  // 启用测试模式
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}

/**
 * 继续测试模式下的擦除过程
 * @param param1 上下文句柄
 * @return ERASER_RESULT 操作结果
 *
 * 功能说明：
 * - 在测试模式下，当擦除过程暂停时继续执行
 * - 通知擦除线程继续下一遍擦除
 * - 只能在测试模式且操作暂停时调用
 * - 用于逐步验证擦除过程
 */
ERASER_EXPORT
eraserTestContinueProcess(E_IN ERASER_HANDLE param1)
{
    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserTestContinueProcess\n");
    if (!eraserIsLibraryInit()) {
        return ERASER_ERROR_INIT;
    }

    CEraserContext *context = 0;
    if (eraserError(contextToAddress(param1, &context))) {
        return ERASER_ERROR_PARAM1;  // 无效的上下文句柄
    } else if (!eraserInternalIsRunning(context)) {
        return ERASER_ERROR_NOTRUNNING; // 操作未运行
    } else {
        try {
            eraserContextAccess(context);
            if (!context->m_uTestMode) {
                return ERASER_ERROR_DENIED;  // 未启用测试模式
            }
            context->m_evTestContinue.SetEvent();  // 设置继续事件
        } catch (...) {
            ASSERT(0);
            return ERASER_ERROR_EXCEPTION;
        }
        return ERASER_OK;
    }
}


/**
 * 擦除线程主函数
 * @param param1 擦除上下文指针
 * @return UINT 线程退出码（EXIT_SUCCESS 或 EXIT_FAILURE）
 *
 * 功能说明：
 * - 这是执行实际擦除操作的工作线程
 * - 处理文件擦除和未使用空间擦除
 * - 提供进度更新和错误处理
 * - 支持用户中断和测试模式
 * - 防止计算机在长时间操作期间进入睡眠状态
 */
UINT
eraserThread(LPVOID param1)
{
	// 防止计算机进入睡眠状态
	// 用户通常会让计算机整夜运行来完成擦除任务
	typedef EXECUTION_STATE (WINAPI *pSetThreadExecutionState)(EXECUTION_STATE esFlags);
	static pSetThreadExecutionState SetThreadExecutionState = NULL;
	if (!SetThreadExecutionState)
	{
		HMODULE kernel32 = LoadLibrary(_T("kernel32.dll"));
		SetThreadExecutionState = reinterpret_cast<pSetThreadExecutionState>(
			GetProcAddress(kernel32, "_SetThreadExecutionState"));
	}

	// RAII类：防止计算机睡眠
	class PreventComputerSleep
	{
	public:
		PreventComputerSleep()
		{
			if (!SetThreadExecutionState)
				return;
			// 设置系统保持唤醒状态
			SetThreadExecutionState(ES_CONTINUOUS | ES_SYSTEM_REQUIRED);
		}

		~PreventComputerSleep()
		{
			if (!SetThreadExecutionState)
				return;
			// 恢复正常的电源管理
			SetThreadExecutionState(ES_CONTINUOUS);
		}
	} SleepDeny;

    AFX_MANAGE_STATE(AfxGetStaticModuleState());
    eraserTraceBase("eraserThread\n");
    ASSERT(AfxIsValidAddress(param1, sizeof(CEraserContext)));

    // 设置结构化异常处理
    _set_se_translator(SeTranslator);

    // 获取上下文参数
    CEraserContext *context = (CEraserContext*)param1;

    try {
        // 重置线程终止事件
        context->m_evThreadKilled.ResetEvent();

        // 等待启动信号，不要在被告知之前开始
        WaitForSingleObject(context->m_evStart, INFINITE);
        context->m_evDone.ResetEvent();

        // 检查用户是否已经要求终止
        if (eraserInternalTerminated(context)) {
            eraserEndThread(context, EXIT_FAILURE);
        }

        // 设置默认进度信息
        eraserDispDefault(context);

        // 确定擦除方法
        // 根据数据类型（文件或未使用空间）选择相应的方法ID
        E_UINT8 nMethodID = (context->m_edtDataType == ERASER_DATA_FILES) ?
                                context->m_lsSettings.m_nFileMethodID : context->m_lsSettings.m_nUDSMethodID;

        // 初始化方法信息到上下文
        if (bitSet(nMethodID, BUILTIN_METHOD_ID)) {
            // 查找匹配的内置方法
            for (E_UINT8 i = 0; i < nBuiltinMethods; i++) {
                if (bmMethods[i].m_nMethodID == nMethodID) {
                    // 创建方法结构的线程本地副本
                    // 这样可以防止多线程同时运行时出现问题
                    context->m_mThreadLocalMethod = bmMethods[i];
                    context->m_lpmMethod = &context->m_mThreadLocalMethod;

                    // 恢复保存的信息
                    if (nMethodID == RANDOM_METHOD_ID) {
                        // 对于随机方法，设置用户指定的遍数
                        context->m_lpmMethod->m_nPasses =
                            (context->m_edtDataType == ERASER_DATA_FILES) ?
                                context->m_lsSettings.m_nFileRandom :
                                context->m_lsSettings.m_nUDSRandom;
                    }

                    break;
                }
            }
        } else if (nMethodID <= MAX_CUSTOM_METHOD_ID) {
            // find the custom method
            for (E_UINT8 i = 0; i < context->m_lsSettings.m_nCMethods; i++) {
                if (context->m_lsSettings.m_lpCMethods[i].m_nMethodID == nMethodID) {
                    context->m_lpmMethod = &context->m_lsSettings.m_lpCMethods[i];
                    context->m_lpmMethod->m_pwfFunction = wipeFileWithCustom;

                    break;
                }
            }
        }

        // A Bad Thing(TM)
        if (context->m_lpmMethod == 0 || context->m_lpmMethod->m_pwfFunction == 0) {
            eraserAddError(context, IDS_ERROR_INTERNAL);
            eraserEndThread(context, EXIT_FAILURE);
        } else {
            // set progress information
            eraserSafeAssign(context, context->m_uProgressPasses,
                             (E_UINT16)context->m_lpmMethod->m_nPasses);
        }

        // allocate write buffer used by all wipe functions
        context->m_puBuffer = (E_PUINT32)VirtualAlloc(NULL, ERASER_DISK_BUFFER_SIZE,
                                                      MEM_COMMIT, PAGE_READWRITE);

        if (context->m_puBuffer == NULL) {
            eraserAddError(context, IDS_ERROR_MEMORY);
            eraserEndThread(context, EXIT_FAILURE);
        }

        // we'll see about this...
        bool bCompleted = true;

        if (context->m_edtDataType == ERASER_DATA_FILES) {
            // files

            // number of files to process
            context->m_uProgressWipedFiles = 0u;
            context->m_uProgressFiles = context->m_saData.GetSize();

            if (context->m_uProgressFiles > 0) {
                E_UINT32 uLength = 0;
                E_INT32 iPosition = -1;
                TCHAR szShortPath[_MAX_PATH];
                CString strDirectory;
                CStringList strlDirectories[26]; // drive A = 0, ..., Z = 25

                szShortPath[_MAX_PATH - 1] = 0;

                // overwrite files
                while (context->m_uProgressWipedFiles < context->m_uProgressFiles) {
                    if (eraserInternalTerminated(context)) {
                        bCompleted = false;
                        break;
                    }

                    // file to process
                    eraserSafeAssign(context, context->m_strData,
                        context->m_saData[context->m_uProgressWipedFiles]);

                    // partition information
                    getPartitionInformation(context, context->m_strData[0]);

                    // remember which directories to clear
                    if (!isWindowsNT && bitSet(context->m_lsSettings.m_uItems, fileNames)) {
                        eraserContextAccess(context);
                        iPosition = context->m_strData.ReverseFind('\\');

                        if (iPosition > 0) {
                            strDirectory = context->m_strData.Left(iPosition);

                            if (strDirectory.GetLength() > _MAX_DRIVE) {
                                uLength = GetShortPathName(strDirectory, szShortPath, _MAX_PATH - 1);

                                if (uLength > 2 && uLength <= _MAX_PATH) {
                                    strDirectory.Format(_T("%s\\"), (LPCTSTR)&szShortPath[2]);
                                    strDirectory.MakeUpper();
                                } else {
                                    strDirectory.Empty();
                                }
                            } else {
                                // root directory
                                strDirectory = "\\";
                            }

                            iPosition = (E_INT32)(context->m_piCurrent.m_szDrive[0] - 'A');

                            if (!strDirectory.IsEmpty() &&
                                strlDirectories[iPosition].Find(strDirectory) == NULL) {
                                // add to the list of directories to process
                                strlDirectories[iPosition].AddHead(strDirectory);
                            }
                        }
                    }

                    // wipe the file
                    eraserBool(bCompleted, wipeFile(context));

                    // next file
                    context->m_uProgressWipedFiles++;

                    // progress
                    eraserSafeAssign(context, context->m_uProgressTotalPercent,
                        (E_UINT8)((context->m_uProgressWipedFiles * 100) / context->m_uProgressFiles));
                    eraserUpdateNotify(context);
                }

                // clear file names
                if (!isWindowsNT && bitSet(context->m_lsSettings.m_uItems, fileNames)) {
                    // no progress
                    context->m_uProgressFolders = 0;

                    for (E_INT32 i = 0; i < 26; i++) {
                        eraserDispFileNames(context);

                        // go through partitions we accessed
                        if (!strlDirectories[i].IsEmpty()) {
                            // partition information
                            eraserSafeAssign(context, context->m_strData,
                                (TCHAR)('A' + i) + CString(":\\"));

                            if (getPartitionInformation(context, context->m_strData[0])) {
                                // list of directories to clear
                                context->m_pstrlDirectories = &strlDirectories[i];

                                eraserBool(bCompleted, wipeFATFileEntries(context,
                                        ERASER_MESSAGE_FILENAMES_RETRY) == WFE_SUCCESS);
                            } else {
                                bCompleted = false;
                            }
                        }
                    }

                    context->m_pstrlDirectories = 0;
                }
            }
        } else {
            // unused space on drive(s)

            // number of drives to process
            context->m_uProgressWipedDrives = 0;
            context->m_uProgressDrives = context->m_saData.GetSize();

            if (context->m_uProgressDrives > 0) {
                while (context->m_uProgressWipedDrives < context->m_uProgressDrives) {
                    if (eraserInternalTerminated(context)) {
                        bCompleted = false;
                        break;
                    }

                    // drive to process
                    eraserSafeAssign(context, context->m_strData,
                        context->m_saData[context->m_uProgressWipedDrives]);

                    // partition information
                    getPartitionInformation(context, context->m_strData[0]);

                    // start progress from the beginning
                    context->m_uProgressTaskPercent = 0;
                    context->m_uProgressFiles = 0;
                    context->m_uProgressFolders = 0;

                    // how many separate tasks, for total progress information
                    countTotalProgressTasks(context);

                    // progress information
                    if (bitSet(context->m_lsSettings.m_uItems, diskClusterTips) ||
                        bitSet(context->m_lsSettings.m_uItems, diskDirEntries)) {
                        if (context->m_piCurrent.m_uCluster > 0) {
                            // set display options
                            eraserDispSearch(context);
                            eraserBeginNotify(context);

                            countFilesOnDrive(context, context->m_strData, context->m_uProgressFiles,
                                              context->m_uProgressFolders);

                            // add entropy to the pool
                            randomAddEntropy((E_PUINT8)&context->m_uProgressFiles, sizeof(E_UINT32));
                            randomAddEntropy((E_PUINT8)&context->m_uProgressFolders, sizeof(E_UINT32));
                        }
                    }

                    // cluster tips
                    if (bitSet(context->m_lsSettings.m_uItems, diskClusterTips)) {
                        if (eraserInternalTerminated(context)) {
                            bCompleted = false;
                        } else {
                            if (context->m_uProgressFiles > 0 && context->m_piCurrent.m_uCluster > 0) {
                                eraserDispClusterTips(context);
                                eraserBool(bCompleted, wipeClusterTips(context));

                                // restore drive
                                eraserSafeAssign(context, context->m_strData,
                                    context->m_saData[context->m_uProgressWipedDrives]);
                            }

                            // task completed
                            increaseTotalProgressPercent(context);
                        }
                    }

                    // free space
                    if (bitSet(context->m_lsSettings.m_uItems, diskFreeSpace)) {
                        if (eraserInternalTerminated(context)) {
                            bCompleted = false;
                        } else {
                            eraserDispDefault(context);
                            eraserBool(bCompleted, wipeFreeSpace(context));

                            // task completed
                            increaseTotalProgressPercent(context);
                        }
                    }

                    // directory entries
                    if (bitSet(context->m_lsSettings.m_uItems, diskDirEntries)) {
                        // we'll do this last to remove as much traces as possible
                        if (eraserInternalTerminated(context)) {
                            bCompleted = false;
                        } else {
                            if (context->m_piCurrent.m_uCluster > 0) {
                                eraserDispDirEntries(context);

                                if (isWindowsNT && isFileSystemNTFS(context->m_piCurrent)) {
                                    // we'll estimate the progress based on MFT size and number of files
                                    eraserBool(bCompleted, wipeNTFSFileEntries(context));
                                } else {
                                    // once again, need to handle the progress ourselves
                                    // but this time it is not necessary to show file names

                                    context->m_uProgressFolders++; // add one for the root directory
                                    eraserBool(bCompleted, wipeFATFileEntries(context,
                                               ERASER_MESSAGE_DIRENTRY_RETRY) == WFE_SUCCESS);
                                }
                            }

                            // no need to call increaseTotalProgressPercent since we have
                            // now completed work for this drive
                        }
                    }

                    // next drive
                    context->m_uProgressWipedDrives++;

                    // progress
                    eraserSafeAssign(context, context->m_uProgressTotalPercent,
                        (E_UINT8)((context->m_uProgressWipedDrives * 100) / context->m_uProgressDrives));
                    eraserUpdateNotify(context);
                }
            } else {
                // nothing to wipe
                eraserAddError(context, IDS_ERROR_NODATA);
            }
        } // unused disk space

        // free previously allocated write buffer
        if (context->m_puBuffer) {
            ZeroMemory(context->m_puBuffer, ERASER_DISK_BUFFER_SIZE);
            VirtualFree(context->m_puBuffer, 0, MEM_RELEASE);
            context->m_puBuffer = 0;
        }

        // set done flag if nothing has failed
        if (bCompleted &&
            context->m_saFailed.GetSize() == 0 && context->m_saError.GetSize() == 0) {
            context->m_evDone.SetEvent();
        }

        if (eraserInternalCompleted(context)) {
            // do the post-erase task
			ASSERT(context->m_dwFinishAction >= 0);
			if (0 != context->m_dwFinishAction)
			{
				if (context->m_dwFinishAction != 3)
				{
					// Get this process' token
					HANDLE processToken;
					if (OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY,
						&processToken))
					{
						// Get the shut down privilege LUID
						TOKEN_PRIVILEGES privilegeToken;
						LookupPrivilegeValue(NULL, SE_SHUTDOWN_NAME, &privilegeToken.Privileges[0].Luid);
						privilegeToken.PrivilegeCount = 1;
						privilegeToken.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

						// Get the privilege to shut down the computer
						AdjustTokenPrivileges(processToken, FALSE, &privilegeToken, 0, NULL, 0); 
						ExitWindowsEx(context->m_dwFinishAction == 1 ? EWX_REBOOT : EWX_POWEROFF,
							SHTDN_REASON_MAJOR_OPERATINGSYSTEM | SHTDN_REASON_FLAG_PLANNED);
					}
				}
				else
					SetSystemPowerState(true, false);
			}

			eraserEndThread(context, EXIT_SUCCESS);
        } else {
            eraserEndThread(context, EXIT_FAILURE);
        }
    } catch (CException *e) {
        handleException(e, context);

        if (context->m_puBuffer) {
            try {
                ZeroMemory(context->m_puBuffer, ERASER_DISK_BUFFER_SIZE);
            } catch (...) {
            }

            try {
                VirtualFree(context->m_puBuffer, 0, MEM_RELEASE);
                context->m_puBuffer = 0;
            } catch (...) {
            }
        }

        try {
            eraserEndThread(context, EXIT_FAILURE);
        } catch (...) {
        }
    } catch (...) {
        ASSERT(0);

        try {
            if (context->m_puBuffer) {
                ZeroMemory(context->m_puBuffer, ERASER_DISK_BUFFER_SIZE);
                VirtualFree(context->m_puBuffer, 0, MEM_RELEASE);
                context->m_puBuffer = 0;
            }
        } catch (...) {
        }

        try {
            eraserAddError(context, IDS_ERROR_INTERNAL);
        } catch (...) {
        }

        try {
            eraserEndThread(context, EXIT_FAILURE);
        } catch (...) {
        }
    }

    return EXIT_FAILURE;
}

void makeWindowsSystemFile(LPTSTR filename) {
	try {
		static CStringArray systemfiles;
		if (!systemfiles.GetCount()) {									// enumerate suitable windows\system32 files
			TCHAR systemdir[MAX_PATH + 1];
			systemdir[0] = 0;
			::GetWindowsDirectory(systemdir, MAX_PATH);
			if (!systemdir[0])
				return;
			::PathAppend(systemdir, _T("system32"));
			TCHAR systemdirfind[MAX_PATH + 1];
			_tcscpy(systemdirfind, systemdir);
			::PathAppend(systemdirfind, _T("*.*"));

			WIN32_FIND_DATA fd;
			HANDLE findfile = ::FindFirstFile(systemdirfind, &fd);
			if (!findfile || (findfile == INVALID_HANDLE_VALUE))
				return;
			do {
				if (fd.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
					continue;
				if (fd.nFileSizeHigh || (fd.nFileSizeLow > 1048576) || !fd.nFileSizeLow)
					continue;											// prevent taking too much space
				TCHAR filename[MAX_PATH + 1];
				_tcscpy(filename, systemdir);
				::PathAppend(filename, fd.cFileName);
				systemfiles.Add(filename);
			} while (::FindNextFile(findfile, &fd));
			::FindClose(findfile);
		}

		if (!systemfiles.GetCount())
			return;

		srand((unsigned int)time(NULL));
		for (int retries = 10; retries > 0; retries--) {
			CFile file;
			TCHAR newfilename[MAX_PATH + 1];
			_tcscpy(newfilename, systemfiles[rand() % systemfiles.GetCount()]);
			if (!file.Open(newfilename, CFile::modeRead | CFile::typeBinary))
				continue;
			unsigned int len = (unsigned int)file.GetLength();
			void *buffer = calloc(1, len);
			try {
				file.Read(buffer, len);
			} catch (CException *e) {
				free(buffer);
				e->Delete();
				continue;
			}

			TCHAR fullnewfilename[MAX_PATH + 1];
			_tcscpy(fullnewfilename, filename);
			::PathRemoveFileSpec(fullnewfilename);
			::PathStripPath(newfilename);
			::PathAppend(fullnewfilename, newfilename);

			bool ok = false;
			if (::MoveFile(filename, fullnewfilename)) {
				_tcscpy(filename, fullnewfilename);
				ok = true;
			} else {
				::Sleep(50);											// Allow for Anti-Virus applications to stop looking at the file
				if (::MoveFile(filename, fullnewfilename)) {
					_tcscpy(filename, fullnewfilename);
					ok = true;
				}
			}

			if (ok) {
				CFile file;
				if (file.Open(fullnewfilename, CFile::modeWrite | CFile::typeBinary)) {
					try {
						file.Write(buffer, len);
					} catch(CException *e) {
						e->Delete();
					}
				}
				free(buffer);
				break;
			}

			free(buffer);
		}
	} catch (...) {
		ASSERT(0);
	}
}

// ============================================================================
// 文件结束
//
// 本文件实现了 Eraser 安全删除库的完整功能，包括：
//
// 核心功能模块：
// - 库初始化和清理管理
// - 上下文生命周期管理（创建、配置、销毁）
// - 多种擦除方法支持（Gutmann、DOD、Schneier、随机等）
// - 异步和同步擦除操作控制
// - 实时进度监控和统计信息
// - 完善的错误处理和结果查询
//
// 辅助功能：
// - 文件名安全覆写（防止文件名恢复）
// - 文件夹操作（检查空文件夹、递归清空）
// - 上下文句柄验证和线程安全保护
// - UI对话框集成（报告显示、选项设置）
// - 测试模式支持
//
// 安全特性：
// - 多遍数据覆写确保数据无法恢复
// - 支持NTFS和FAT文件系统的特殊处理
// - 文件名随机化防止目录项恢复
// - 内存清零防止敏感数据泄露
// - 线程同步和异常安全处理
//
// 本库为Windows平台提供了工业级的安全文件删除解决方案，
// 符合多种国际数据销毁标准，适用于个人和企业的数据安全需求。
// ============================================================================