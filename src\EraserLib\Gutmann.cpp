﻿// Gutmann.cpp - Gutmann 35遍安全擦除算法实现
//
// 实现了<PERSON>在论文《Secure Deletion of Data from Magnetic and
// Solid-State Memory》(USENIX, 1996)中描述的文件擦除方法。
//
// Gutmann算法是最彻底的数据擦除方法之一，通过35遍不同的数据模式
// 覆写来确保数据无法通过任何已知的数据恢复技术恢复。
//
// 算法特点：
// - 35遍擦除，包括随机数据和特定的磁性模式
// - 针对不同类型的磁性存储介质优化
// - 考虑了磁性残留和相邻磁道干扰
// - 被认为是最安全的数据擦除标准之一
//
// Eraser. Secure data removal. For Windows.
// Copyright ©1997-2001  Sami <PERSON> (<EMAIL>).
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version 2
// of the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program; if not, write to the Free Software
// Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA
// 02111-1307, USA.

#include "stdafx.h"
#include "EraserDll.h"
#include "Common.h"
#include "Gutmann.h"

/**
 * 使用Gutmann方法擦除文件
 * @param context 擦除上下文指针
 * @return bool 成功返回true，失败返回false
 *
 * 功能说明：
 * - 执行Peter Gutmann的35遍擦除算法
 * - 使用随机数据和特定磁性模式的组合
 * - 随机化遍数顺序以增强安全性
 * - 提供最高级别的数据擦除保护
 */
bool
wipeFileWithGutmann(CEraserContext *context)
{
    LPPASS   passGutmann = context->m_lpmMethod->m_lpPasses;  // Gutmann擦除遍数数组
    E_UINT32 uStartTime = GetTickCount();                    // 开始时间
    E_UINT32 uUsedSize  = 0;                                 // 当前使用的缓冲区大小
    E_UINT32 uSavedSize = 0;                                 // 保存的缓冲区大小
    E_UINT64 uLength    = 0;                                 // 剩余要擦除的长度
    E_UINT64 uWiped     = 0;                                 // 已擦除的总字节数
    E_UINT32 uWritten   = 0;                                 // 实际写入的字节数
    bool     bCompleted = true;                              // 操作完成标志

    // 发送开始通知（仅发送一次）
    postStartNotification(context);

    // 设置缓冲区大小
    setBufferSize(context, uSavedSize);

    // 随机化遍数数组的顺序（除了随机遍数）
    // 这增加了额外的安全性，使攻击者更难预测擦除模式
    shufflePassArray((LPPASS)(passGutmann + PASSES_GUTMANN_RANDOM),
                     (PASSES_GUTMANN - 2 * PASSES_GUTMANN_RANDOM));

    // 主擦除循环：执行Gutmann的35遍擦除
    for (E_UINT16 uCurrentPass = 0; uCurrentPass < PASSES_GUTMANN; uCurrentPass++) {
        // 更新当前遍数（用于进度显示）
        eraserSafeAssign(context, context->m_uProgressCurrentPass, (E_UINT16)(uCurrentPass + 1));

        // 重新定位到文件开始位置
        // 每一遍都要从头开始覆盖整个文件
        SetFilePointer(context->m_hFile, context->m_uiFileStart.LowPart,
                       (E_PINT32)&context->m_uiFileStart.HighPart, FILE_BEGIN);

        uLength = context->m_uiFileSize.QuadPart;  // 重置剩余长度
        uUsedSize = uSavedSize;                    // 重置缓冲区大小

        // 用当前遍数的数据模式填充缓冲区
        // Gutmann算法使用35种不同的数据模式
        fillPassData(context->m_puBuffer, uUsedSize, &passGutmann[uCurrentPass]);

        // 内层循环：逐块写入数据直到整个文件被覆盖
        while (uLength > 0) {
            // 如果是随机数据遍数，需要重新填充随机数据
            // Gutmann算法的前4遍和后4遍使用随机数据
            if (isRandomPass(passGutmann[uCurrentPass])) {
                isaacFill((E_PUINT8)context->m_puBuffer, uUsedSize);
            }

            // 调整缓冲区大小以适应剩余数据
            // 最后一块可能小于完整缓冲区大小
            if (uLength < (E_UINT64)uUsedSize) {
                uUsedSize = (E_UINT32)uLength;
            }

            // 执行写入操作
            // 只有在未被终止且写入成功时才认为完成
            bCompleted = !eraserInternalTerminated(context) &&
                         WriteFile(context->m_hFile, context->m_puBuffer,
                                   uUsedSize, &uWritten, NULL) &&
                         (uUsedSize == uWritten);

            // 强制将数据刷新到磁盘
            // 确保数据真正写入存储介质而不是停留在缓存中
            FlushFileBuffers(context->m_hFile);

            // 如果写入失败或被终止，停止操作
            if (!bCompleted) {
                break;
            }

            // 更新统计信息
            context->m_uProgressWiped += (E_UINT64)uUsedSize;  // 总擦除字节数
            uWiped += (E_UINT64)uUsedSize;                     // 本次擦除字节数

            // 更新剩余长度
            uLength -= (E_UINT64)uUsedSize;

            // 发送进度更新通知
            postUpdateNotification(context, PASSES_GUTMANN);
        }

        // 测试模式：在每遍完成后暂停
        // 允许用户检查擦除结果的有效性
        if (context->m_uTestMode && !eraserInternalTerminated(context)) {
            context->m_evTestContinue.ResetEvent();     // 重置继续事件
            eraserTestPausedNotify(context);            // 通知测试暂停
            WaitForSingleObject(context->m_evTestContinue, INFINITE);  // 等待继续信号
        }

        // 如果当前遍数未完成，退出整个擦除过程
        if (!bCompleted) {
            break;
        }
    }

    // 设置最终统计信息
    setEndStatistics(context, uWiped, uStartTime);

    // 安全考虑：遍数数组可能会向潜在的入侵者透露覆写遍数的顺序
    // 虽然我们不能将数组清零（因为它是预定义的），但我们可以在操作后
    // 再次随机化它。这真的有必要吗？我不知道，但小心总是没错的。

    // 再次随机化遍数数组以消除遍数顺序的痕迹
    // 这是一个额外的安全措施，防止攻击者通过内存分析获得擦除模式信息
    shufflePassArray((LPPASS)(passGutmann + PASSES_GUTMANN_RANDOM),
                     (PASSES_GUTMANN - 2 * PASSES_GUTMANN_RANDOM));

    return bCompleted;
}

// ============================================================================
// 文件结束
//
// 本文件实现了Peter Gutmann的35遍安全擦除算法，这是目前最彻底的
// 数据擦除方法之一。该算法考虑了磁性存储介质的物理特性，通过
// 35种不同的数据模式确保原始数据无法通过任何已知技术恢复。
//
// 算法特点：
// - 35遍擦除提供最高级别的安全性
// - 随机化遍数顺序增强安全性
// - 针对磁性存储介质的物理特性优化
// - 包含随机数据和特定磁性模式
// - 严格的安全措施防止信息泄露
//
// 该算法适用于需要最高安全级别的数据销毁场景，如军事、
// 政府机构和处理高度敏感信息的企业。
// ============================================================================
