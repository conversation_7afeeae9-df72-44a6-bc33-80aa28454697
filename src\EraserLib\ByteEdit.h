﻿#if !defined(AFX_BYTEEDIT_H__2E652310_BC8C_11D3_82A0_000000000000__INCLUDED_)
#define AFX_BYTEEDIT_H__2E652310_BC8C_11D3_82A0_000000000000__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

/////////////////////////////////////////////////////////////////////////////
// CByteEdit window

class  CByteEdit : public CEdit
{
// Construction
public:
    CByteEdit();

// Attributes
public:

// Operations
public:

// Overrides
    // ClassWizard generated virtual function overrides
    //{{AFX_VIRTUAL(CByteEdit)
    public:
    virtual BOOL Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext = NULL);
    //}}AFX_VIRTUAL

// Implementation
public:
    void SetByte(BYTE);
    BYTE GetByte();
    virtual ~CByteEdit();

    // Generated message map functions
protected:
    //{{AFX_MSG(CByteEdit)
    afx_msg void OnChar(UINT nChar, UINT nRepCnt, UINT nFlags);
    afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
    afx_msg void OnRButtonDown(UINT nFlags, CPoint point);
    //}}AFX_MSG

    DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BYTEEDIT_H__2E652310_BC8C_11D3_82A0_000000000000__INCLUDED_)
