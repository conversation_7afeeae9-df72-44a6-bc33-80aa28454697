// Erasext.odl : type library source for Erasext.dll

// This file will be processed by the Make Type Library (mktyplib) tool to
// produce the type library (Erasext.tlb).

[ uuid(8BE13460-936F-11D1-A87D-************), version(1.0) ]
library Erasext
{
	importlib("stdole32.tlb");
	

	//  Primary dispatch interface for CErasextMenu
	
	[ uuid(8BE13461-936F-11D1-A87D-************) ]
	dispinterface IErasextMenu
	{
		properties:
			// NOTE - ClassWizard will maintain property information here.
			//    Use extreme caution when editing this section.
			//{{AFX_ODL_PROP(CErasextMenu)
			//}}AFX_ODL_PROP
			
		methods:
			// NOTE - ClassWizard will maintain method information here.
			//    Use extreme caution when editing this section.
			//{{AFX_ODL_METHOD(CErasextMenu)
			//}}AFX_ODL_METHOD

	};

	//  Class information for CErasextMenu

	[ uuid(8BE13462-936F-11D1-A87D-************) ]
	coclass ERASEXTMENU
	{
		[default] dispinterface IErasextMenu;
	};

	//{{AFX_APPEND_ODL}}
};
