// EraserLibStatic.h - Eraser 静态库使用指南
// 
// 本文件提供了使用 EraserLib 静态库的完整指南和示例代码
// 
// 版权信息：
// Eraser. Secure data removal. For Windows.
// Copyright ©1997-2001  <PERSON> (<EMAIL>).
// Copyright ©2001-2006  Garrett Trant (<EMAIL>).
// Copyright ©2007 The Eraser Project
//
// 本程序是自由软件；您可以根据自由软件基金会发布的GNU通用公共许可证
// 第2版或（根据您的选择）任何更高版本的条款重新分发和/或修改它。

#ifndef ERASER_LIB_STATIC_H
#define ERASER_LIB_STATIC_H

// ============================================================================
// 静态库使用说明
// ============================================================================

/*
使用 EraserLib 静态库的步骤：

1. 项目配置
   - 将 EraserLib.lib 添加到您的项目依赖库中
   - 确保包含路径中有 EraserLib 的头文件目录
   - 在预处理器定义中添加 _LIB_ERASER

2. 包含头文件
   #include "EraserDll.h"  // 主要的API接口

3. 链接依赖库
   - EraserLib.lib (本库)
   - EraserUI.lib (如果使用UI组件)
   - Shared.lib (共享组件)
   - netapi32.lib (系统库)

4. 运行时库设置
   - Debug 配置：多线程调试 (/MTd)
   - Release 配置：多线程 (/MT)

5. MFC 设置
   - 使用静态 MFC 库
*/

// ============================================================================
// 基本使用示例
// ============================================================================

/*
// 示例1：简单文件擦除
#include "EraserDll.h"

int main()
{
    ERASER_HANDLE handle;
    ERASER_RESULT result;
    
    // 1. 初始化库
    result = eraserInit();
    if (eraserError(result)) {
        printf("初始化失败: %d\n", result);
        return -1;
    }
    
    // 2. 创建擦除上下文
    result = eraserCreateContext(&handle);
    if (eraserError(result)) {
        printf("创建上下文失败: %d\n", result);
        eraserEnd();
        return -1;
    }
    
    // 3. 设置数据类型为文件
    result = eraserSetDataType(handle, ERASER_DATA_FILES);
    if (eraserError(result)) {
        printf("设置数据类型失败: %d\n", result);
        eraserDestroyContext(handle);
        eraserEnd();
        return -1;
    }
    
    // 4. 添加要擦除的文件
    const char* filename = "C:\\temp\\secret.txt";
    result = eraserAddItem(handle, (LPVOID)filename, strlen(filename));
    if (eraserError(result)) {
        printf("添加文件失败: %d\n", result);
        eraserDestroyContext(handle);
        eraserEnd();
        return -1;
    }
    
    // 5. 开始同步擦除
    result = eraserStartSync(handle);
    if (eraserError(result)) {
        printf("擦除失败: %d\n", result);
    } else {
        printf("文件擦除成功\n");
    }
    
    // 6. 清理资源
    eraserDestroyContext(handle);
    eraserEnd();
    
    return 0;
}
*/

// ============================================================================
// 高级使用示例
// ============================================================================

/*
// 示例2：使用特定擦除方法和进度监控
#include "EraserDll.h"
#include <windows.h>

// 进度回调窗口过程
LRESULT CALLBACK ProgressWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    if (msg == WM_ERASERNOTIFY) {
        ERASER_HANDLE handle = (ERASER_HANDLE)lParam;
        
        switch (wParam) {
        case ERASER_WIPE_BEGIN:
            printf("开始擦除...\n");
            break;
            
        case ERASER_WIPE_UPDATE:
            {
                E_UINT8 percent;
                if (eraserOK(********************(handle, &percent))) {
                    printf("进度: %d%%\n", percent);
                }
            }
            break;
            
        case ERASER_WIPE_DONE:
            {
                E_UINT8 completed;
                if (eraserOK(eraserCompleted(handle, &completed)) && completed) {
                    printf("擦除完成\n");
                } else {
                    printf("擦除失败或被中断\n");
                }
            }
            break;
        }
    }
    return DefWindowProc(hwnd, msg, wParam, lParam);
}

int AdvancedEraseExample()
{
    ERASER_HANDLE handle;
    ERASER_RESULT result;
    HWND hwnd;
    
    // 创建消息窗口
    WNDCLASS wc = {0};
    wc.lpfnWndProc = ProgressWndProc;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = L"EraserProgress";
    RegisterClass(&wc);
    
    hwnd = CreateWindow(L"EraserProgress", L"", 0, 0, 0, 0, 0, 
                       HWND_MESSAGE, NULL, GetModuleHandle(NULL), NULL);
    
    // 初始化库
    result = eraserInit();
    if (eraserError(result)) return -1;
    
    // 创建自定义上下文 (使用Gutmann方法，35遍，擦除文件内容和文件名)
    result = eraserCreateContextEx(&handle, 
                                  convEraseMethod(ERASER_METHOD_GUTMANN),
                                  35,
                                  fileClusterTips | fileNames);
    if (eraserError(result)) {
        eraserEnd();
        return -1;
    }
    
    // 设置进度通知窗口
    result = eraserSetWindow(handle, hwnd);
    if (eraserError(result)) {
        eraserDestroyContext(handle);
        eraserEnd();
        return -1;
    }
    
    // 设置数据类型
    result = eraserSetDataType(handle, ERASER_DATA_FILES);
    if (eraserError(result)) {
        eraserDestroyContext(handle);
        eraserEnd();
        return -1;
    }
    
    // 添加文件
    const char* filename = "C:\\temp\\topsecret.doc";
    result = eraserAddItem(handle, (LPVOID)filename, strlen(filename));
    if (eraserError(result)) {
        eraserDestroyContext(handle);
        eraserEnd();
        return -1;
    }
    
    // 开始异步擦除
    result = eraserStart(handle);
    if (eraserError(result)) {
        eraserDestroyContext(handle);
        eraserEnd();
        return -1;
    }
    
    // 消息循环等待完成
    MSG msg;
    E_UINT8 running = 1;
    while (running) {
        if (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        // 检查是否还在运行
        eraserIsRunning(handle, &running);
        Sleep(100);
    }
    
    // 清理
    eraserDestroyContext(handle);
    eraserEnd();
    DestroyWindow(hwnd);
    
    return 0;
}
*/

// ============================================================================
// 错误处理最佳实践
// ============================================================================

/*
// 示例3：完整的错误处理
#include "EraserDll.h"

const char* GetErrorString(ERASER_RESULT error)
{
    switch (error) {
    case ERASER_OK: return "成功";
    case ERASER_ERROR: return "未指定错误";
    case ERASER_ERROR_PARAM1: return "参数1无效";
    case ERASER_ERROR_PARAM2: return "参数2无效";
    case ERASER_ERROR_MEMORY: return "内存不足";
    case ERASER_ERROR_THREAD: return "线程启动失败";
    case ERASER_ERROR_INIT: return "库未初始化";
    case ERASER_ERROR_RUNNING: return "操作正在运行";
    case ERASER_ERROR_NOTRUNNING: return "操作未运行";
    case ERASER_ERROR_DENIED: return "权限不足";
    default: return "未知错误";
    }
}

int SafeEraseFile(const char* filename)
{
    ERASER_HANDLE handle = ERASER_INVALID_CONTEXT;
    ERASER_RESULT result;
    
    // 初始化
    result = eraserInit();
    if (eraserError(result)) {
        printf("初始化失败: %s\n", GetErrorString(result));
        return result;
    }
    
    // 创建上下文
    result = eraserCreateContext(&handle);
    if (eraserError(result)) {
        printf("创建上下文失败: %s\n", GetErrorString(result));
        goto cleanup_init;
    }
    
    // 验证上下文
    result = eraserIsValidContext(handle);
    if (eraserError(result)) {
        printf("上下文无效: %s\n", GetErrorString(result));
        goto cleanup_context;
    }
    
    // 设置数据类型
    result = eraserSetDataType(handle, ERASER_DATA_FILES);
    if (eraserError(result)) {
        printf("设置数据类型失败: %s\n", GetErrorString(result));
        goto cleanup_context;
    }
    
    // 添加文件
    result = eraserAddItem(handle, (LPVOID)filename, strlen(filename));
    if (eraserError(result)) {
        printf("添加文件失败: %s\n", GetErrorString(result));
        goto cleanup_context;
    }
    
    // 执行擦除
    result = eraserStartSync(handle);
    if (eraserError(result)) {
        printf("擦除失败: %s\n", GetErrorString(result));
        goto cleanup_context;
    }
    
    // 检查完成状态
    E_UINT8 completed;
    result = eraserCompleted(handle, &completed);
    if (eraserError(result) || !completed) {
        printf("擦除未完成: %s\n", GetErrorString(result));
        goto cleanup_context;
    }
    
    printf("文件 '%s' 擦除成功\n", filename);
    result = ERASER_OK;

cleanup_context:
    if (handle != ERASER_INVALID_CONTEXT) {
        eraserDestroyContext(handle);
    }
    
cleanup_init:
    eraserEnd();
    
    return result;
}
*/

#endif // ERASER_LIB_STATIC_H
