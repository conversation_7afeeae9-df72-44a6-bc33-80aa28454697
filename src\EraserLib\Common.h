// Common.h - Eraser 库公共定义和工具函数头文件
//
// 本文件包含了 Eraser 库内部使用的公共定义、全局变量、宏定义和工具函数
// 主要功能包括：
// - 全局变量定义和初始化控制
// - 上下文数组管理和线程同步
// - 库初始化和引用计数管理
// - 各种辅助宏和工具函数
// - 文件系统检测和操作
// - 进度通知和统计信息处理

#ifndef COMMON_H
#define COMMON_H

// ============================================================================
// 全局变量定义控制
// ============================================================================
// 这些宏用于控制全局变量的定义和声明
// 在一个源文件中定义 GLOBAL_VARIABLES_HERE 来实际定义变量
// 在其他文件中这些变量将被声明为 extern
#ifdef GLOBAL_VARIABLES_HERE
    #define GLOBALVAR                         // 实际定义变量
    #define GLOBALINIT1(x)    (x)            // 单参数初始化
    #define GLOBALINIT2(x, y) (x, y)         // 双参数初始化
#else
    #define GLOBALVAR extern                 // 外部声明
    #define GLOBALINIT1(x)                   // 忽略初始化
    #define GLOBALINIT2(x, y)                // 忽略初始化
#endif

#include "random.h"                          // 随机数生成器
#include "eraserdllinternal.h"               // 库内部定义

// ============================================================================
// 擦除方法ID定义
// ============================================================================
// 内置擦除方法的标识符常量
#define GUTMANN_METHOD_ID    0x80  // Gutmann 35遍擦除方法
#define DOD_METHOD_ID        0x81  // 美国国防部标准方法
#define DOD_E_METHOD_ID      0x82  // 美国国防部扩展方法
#define RANDOM_METHOD_ID     0x83  // 伪随机数据擦除方法
#define FL2KB_METHOD_ID      0x84  // 仅擦除文件首末2KB方法
#define SCHNEIER_METHOD_ID   0x85  // Schneier 7遍擦除方法

// ============================================================================
// 上下文数组管理
// ============================================================================
// 全局上下文数组，存储所有活动的擦除上下文
GLOBALVAR CEraserContext *eraserContextArray[ERASER_MAX_CONTEXT + 1];

// 上下文数组的线程同步临界区
// 确保多线程环境下对上下文数组的安全访问
GLOBALVAR CCriticalSection csContextArray;

// ============================================================================
// 上下文访问控制辅助宏
// ============================================================================
// 获取上下文数组访问权限（加锁）
#define eraserContextArrayAccess() \
    eraserTraceLock("eraserContextArrayAccess\n"); \
    CSingleLock sl(&csContextArray, TRUE)

// 释放上下文数组访问权限（解锁）
#define eraserContextArrayRelease() \
    eraserTraceLock("eraserContextArrayRelease\n"); \
    sl.Unlock()

// 重新获取上下文数组访问权限
#define eraserContextArrayRelock() \
    eraserTraceLock("eraserContextArrayRelock\n"); \
    sl.Lock()

// ============================================================================
// 库初始化控制
// ============================================================================
// 引用计数的线程同步临界区
GLOBALVAR CCriticalSection csReferenceCount;

// 库引用计数器（跟踪有多少个实例在使用库）
GLOBALVAR E_UINT16 uReferenceCount GLOBALINIT1(0);

// 库初始化事件（用于标识库是否已初始化）
GLOBALVAR CEvent evLibraryInitialized GLOBALINIT2(FALSE, TRUE);

// Eraser 互斥体名称（用于进程间同步）
const LPTSTR strEraserMutex = _T("Eraser-D309F296-B70C-473d-B2DE-2A1F9C7C9FB1");

// ============================================================================
// 库状态检查和控制宏
// ============================================================================
// 检查库是否已初始化
#define eraserIsLibraryInit() \
    (WaitForSingleObject(evLibraryInitialized, 0) == WAIT_OBJECT_0)

// 初始化库（增加引用计数）
#define eraserLibraryInit() \
    evLibraryInitialized.SetEvent(); \
    csReferenceCount.Lock(); \
    uReferenceCount++; \
	if (uReferenceCount == 1) \
	{ \
		/* 创建安全描述符，允许所有用户访问 */ \
		SECURITY_DESCRIPTOR sc; \
		InitializeSecurityDescriptor(&sc, SECURITY_DESCRIPTOR_REVISION); \
		SetSecurityDescriptorDacl(&sc, TRUE, NULL, FALSE); \
		\
		SECURITY_ATTRIBUTES attr; \
		attr.nLength = sizeof(attr); \
		attr.lpSecurityDescriptor = &sc; \
		attr.bInheritHandle = FALSE; \
		\
		/* 创建本地和全局互斥体以防止多个Eraser实例同时运行 */ \
		CreateMutex(&attr, TRUE, strEraserMutex); \
		CreateMutex(&attr, TRUE, CString(_T("Global\\")) + strEraserMutex); \
	} \
    csReferenceCount.Unlock()

// 反初始化库（减少引用计数）
#define eraserLibraryUninit() \
    csReferenceCount.Lock(); \
    if (uReferenceCount > 0) { \
        uReferenceCount--; \
    } \
    /* 当引用计数为0时，释放互斥体并重置初始化事件 */ \
    if (uReferenceCount == 0) { \
		CMutex localMutex(FALSE, _T("Eraser-D309F296-B70C-473d-B2DE-2A1F9C7C9FB1")); \
		CMutex globalMutex(FALSE, _T("Global\\Eraser-D309F296-B70C-473d-B2DE-2A1F9C7C9FB1")); \
		localMutex.Unlock(); \
		globalMutex.Unlock(); \
        evLibraryInitialized.ResetEvent(); \
    } \
    csReferenceCount.Unlock()

// 强制解锁库（用于清理）
#define eraserLibraryUnlock() \
    csReferenceCount.Lock(); \
    uReferenceCount = 0; \
    evLibraryInitialized.ResetEvent(); \
    csReferenceCount.Unlock()


// ============================================================================
// 其他全局变量
// ============================================================================
// Windows NT 系列系统标识（影响某些文件系统特性的可用性）
GLOBALVAR bool isWindowsNT;
bool ERASER_API IsWindowsNT();

// 异常信息缓冲区
const E_UINT16 uExceptionBufferSize = 127;
GLOBALVAR TCHAR szExceptionBuffer[uExceptionBufferSize];


// ============================================================================
// 文件名安全字符定义
// ============================================================================
// 用于文件名随机化的安全字符数组大小
const E_UINT16 ERASER_SAFEARRAY_SIZE = 36;
// 安全字符数组（排除了一些不常用的字符，确保文件名兼容性）
const LPCTSTR ERASER_SAFEARRAY = _T("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");

/**
 * 创建随机文件名
 * @param szFileName 输出缓冲区，存储生成的文件名
 * @param uNameLength 文件名长度（不包括扩展名）
 * @param uExtLength 扩展名长度
 * @param uCounter 计数器（用于确保文件名唯一性）
 *
 * 注意：szFileName 缓冲区至少需要 (uNameLength + 1 + uExtLength + 1) 字节
 *
 * 功能说明：
 * - 生成由安全字符组成的随机文件名
 * - 支持指定文件名和扩展名长度
 * - 使用计数器确保文件名的唯一性
 * - 自动处理路径长度限制
 */
inline void createRandomFileName(LPTSTR szFileName, E_UINT16 uNameLength, E_UINT16 uExtLength, E_UINT16 uCounter)
{
    try {
        E_UINT32 uLength = uNameLength;

        // 如果有扩展名，计算总长度（包括点号分隔符）
        if (uExtLength > 0) {
            uLength += uExtLength + 1;
        }

        // 确保不超过最大路径长度限制
        if (uLength >= MAX_PATH) {
            uLength = MAX_PATH - 1;
        }

        E_UINT8 uRandomArray[MAX_PATH];
        isaacFill(uRandomArray, MAX_PATH);

        for (E_UINT32 uIndex = 0; uIndex < uLength; uIndex++) {
            szFileName[uIndex] = ERASER_SAFEARRAY[uRandomArray[uIndex] % ERASER_SAFEARRAY_SIZE];
        }

        ZeroMemory(uRandomArray, MAX_PATH);

        if (uCounter > 0 && uNameLength >= 4) {
            _sntprintf(&szFileName[uNameLength - 4], 4, _T("%04X"), uCounter);
        }

        if (uExtLength > 0) {
            szFileName[uNameLength] = '.';
        }

        szFileName[uLength] = 0;
    } catch (...) {
        ASSERT(0);
    }
}

const E_UINT16 uShortFileNameLength = 8 + 1 + 3;
#define createRandomShortFileName(szFileName, uCounter) \
    createRandomFileName((szFileName), 8, 3, (uCounter))


// common helpers for all wipe functions to handle notification and
// statistics
//
inline void
countTotalProgressTasks(CEraserContext *context)
{
    // erasing unused disk space is divided to three steps when
    // it comes to showing total progress

    context->m_uProgressTasks = 0;
    if (bitSet(context->m_lsSettings.m_uItems, diskClusterTips)) {
        context->m_uProgressTasks++;
    }
    if (bitSet(context->m_lsSettings.m_uItems, diskFreeSpace)) {
        context->m_uProgressTasks++;
        if (isWindowsNT && isFileSystemNTFS(context->m_piCurrent)) {  // MFT records
            context->m_uProgressTasks++;
        }
    }
    if (bitSet(context->m_lsSettings.m_uItems, diskDirEntries)) {
        context->m_uProgressTasks++;
    }
    if (context->m_uProgressTasks < 1) {
        context->m_uProgressTasks = 1;
    }
}

#pragma warning(disable : 4244)

inline void
increaseTotalProgressPercent(CEraserContext *context)
{
    // one task has been completed, increase m_uProgressTaskPercent
    if (context->m_uProgressTasks > 1) {
        context->m_uProgressTaskPercent += 100 / context->m_uProgressTasks;
    }
}

#pragma warning(default : 4244)

inline void
setTotalProgress(CEraserContext *context)
{
    eraserContextAccess(context);

    if (context->m_edtDataType == ERASER_DATA_FILES) {
        context->m_uProgressTotalPercent =
            (E_UINT8)( ((context->m_uProgressWipedFiles * 100) / context->m_uProgressFiles) +
                       (context->m_uProgressPercent / context->m_uProgressFiles));
    } else {
        context->m_uProgressTotalPercent =
            (E_UINT8)( ((context->m_uProgressWipedDrives * 100 + context->m_uProgressTaskPercent) /
                            context->m_uProgressDrives) +
                       (context->m_uProgressPercent / (context->m_uProgressTasks * context->m_uProgressDrives)));
    }
}

inline void
postStartNotification(CEraserContext *context)
{
    // send update only when starting the overwriting
    if (!bitSet(context->m_uProgressFlags, progressCustom) && context->m_uProgressWiped == 0) {
        eraserBeginNotify(context);
    }
}

inline void
postUpdateNotification(CEraserContext *context, E_UINT16 passes)
{
    if (!bitSet(context->m_uProgressFlags, progressCustom)) {
        eraserContextAccess(context);

        context->m_uProgressPercent =
            (E_UINT8)(((E_UINT64)(context->m_uProgressWiped * 100)) /
                     ((E_UINT64)(context->m_uProgressSize * passes)));

        setTotalProgress(context);

        E_UINT32 uTickCount = GetTickCount();
        if (uTickCount > context->m_uProgressStartTime) {
            E_UINT64 uSpeed =
                context->m_uProgressWiped / (E_UINT64)(uTickCount - context->m_uProgressStartTime);

            if (uSpeed > 0) {
                context->m_uProgressTimeLeft =
                    (E_UINT32)(((context->m_uProgressSize * passes) - context->m_uProgressWiped) /
                             (uSpeed * 1000));
            } else {
                context->m_uProgressTimeLeft = 0;
            }
        }

        eraserUpdateNotifyNoAccess(context);
    }
}

/**
 * 设置擦除操作结束时的统计信息
 * @param context 擦除上下文指针
 * @param uWiped 实际擦除的字节数
 * @param uPrevTime 操作开始时的时间戳
 *
 * 功能说明：
 * - 更新总擦除字节数统计
 * - 计算实际擦除的区域大小
 * - 统计簇尾部（slack space）的擦除情况
 * - 计算操作耗时
 */
inline void
setEndStatistics(CEraserContext *context, E_UINT64& uWiped, E_UINT32& uPrevTime)
{
    E_UINT32 uTickCount = GetTickCount();

    // 更新总擦除字节数
    context->m_uStatWiped += uWiped;

    // 如果指定区域至少被完全覆写一次
    if (uWiped >= context->m_uiFileSize.QuadPart) {
        context->m_uStatErasedArea += context->m_uiFileSize.QuadPart;
        context->m_uStatTips += context->m_uClusterSpace;
    } else {
        // 整个区域没有被完全覆写
        context->m_uStatErasedArea += uWiped;

        // 计算簇尾部实际被覆写的部分
        if (context->m_uClusterSpace > 0 &&
            uWiped > (context->m_uiFileSize.QuadPart - context->m_uClusterSpace)) {
            // 计算簇尾部实际被覆写的字节数
            context->m_uStatTips += context->m_uiFileSize.QuadPart -
                                    context->m_uClusterSpace;
        }
    }

    // 更新操作耗时统计
    if (uTickCount > uPrevTime) {
        context->m_uStatTime += (uTickCount - uPrevTime);
    }
}


#endif

// ============================================================================
// 文件结束
//
// 本头文件定义了 Eraser 库的核心基础设施，包括：
//
// 核心功能：
// - 全局变量管理和线程同步机制
// - 库初始化和引用计数控制
// - 上下文数组管理和访问控制
// - 进程间互斥体管理
//
// 工具函数：
// - 随机文件名生成
// - 短文件名创建
// - 进度通知和统计信息处理
// - 文件系统类型检测
//
// 安全特性：
// - 线程安全的全局状态管理
// - 安全的文件名字符集
// - 完善的异常处理支持
// - 内存和资源清理机制
//
// 这些基础设施确保了 Eraser 库在多线程环境下的稳定运行，
// 并为上层的擦除算法提供了可靠的支撑。
// ============================================================================