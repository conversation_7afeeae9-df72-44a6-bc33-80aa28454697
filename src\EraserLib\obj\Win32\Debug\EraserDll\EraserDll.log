﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1216,5): warning MSB8012: TargetPath(C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Debug\EraserDll.dll) 与 Linker 的 OutputFile 属性值(C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Debug\Eraser.dll)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1218,5): warning MSB8012: TargetName(EraserDll) 与 Linker 的 OutputFile 属性值(Eraser)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
ByteEdit.obj : warning LNK4075: 忽略“/EDITANDCONTINUE”(由于“/SAFESEH”规范)
    正在创建库 C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Debug\EraserDll.lib 和对象 C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Debug\EraserDll.exp
  EraserDll.vcxproj -> C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Debug\EraserDll.dll
