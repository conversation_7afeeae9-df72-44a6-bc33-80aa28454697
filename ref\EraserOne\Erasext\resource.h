//{{NO_DEPENDENCIES}}
// Microsoft Developer Studio generated include file.
// Used by <PERSON>sext.rc
//
#define IDS_MENU_TEXT_FILE              1
#define IDS_MENU_TEXT_DRIVE             2
#define IDS_COMMAND_STRING_FILE         3
#define IDC_BUTTON_NOTOALL              3
#define IDS_COMMAND_STRING_DRIVE        4
#define IDS_CONFIRM_FILES               7
#define IDS_CONFIRM_DRIVE               8
#define IDOPTIONS                       9
#define IDS_COMMAND_STRING_DIRECTORIES  9
#define IDS_CONFIRM_FILES_AND_FOLDERS   10
#define IDS_CONFIRM                     11
#define IDS_MENU_TEXT_DRAG              12
#define IDS_CONFIRM_MOVE                13
#define IDS_CONFIRM_MOVE_FILES          14
#define IDS_CONFIRM_MOVE_FILES_AND_FOLDERS 15
#define IDS_CONFIRM_MULTI_DRIVE         16
#define IDS_CONFIRM_MOVE_FILE           28
#define IDS_MOVE_TITLE                  29
#define IDS_ERROR_MOVE_SAMEFOLDER       30
#define IDM_ERASEXT_MENU                101
#define IDD_DIALOG_WIPEPROG             129
#define IDI_ICON_ERASER                 130
#define IDD_DIALOG_CONFIRM              131
#define IDR_ACCELERATOR_CONFIRM         132
#define IDR_ACCELERATOR_PROG            133
#define IDD_DIALOG_REPLACE              136
#define IDI_ICON_REPLACE                137
#define IDC_STATIC_PERCENT              1002
#define IDC_STATIC_LINETWO              1007
#define IDC_STATIC_HEADER               1008
#define IDC_STATIC_EXISTING             1009
#define IDC_STATIC_SOURCE               1010
#define IDC_BUTTON_YESTOALL             1011
#define IDC_CHECK_RESULTS               1012
#define IDC_STATIC_LINEONE              1013
#define IDI_ICON_EXISTING               1014
#define IDI_ICON_SOURCE                 1015
#define IDC_PROGRESS                    1025
#define IDC_STATIC_ERASING              1026
#define IDC_STATIC_MESSAGE              1027
#define IDC_STATIC_DATA                 1028
#define IDC_STATIC_PASS                 1029
#define IDC_STATIC_TIME                 1030
#define IDC_PROGRESS_TOTAL              1032
#define IDC_STATIC_PERCENT_TOTAL        1033

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        138
#define _APS_NEXT_COMMAND_VALUE         32771
#define _APS_NEXT_CONTROL_VALUE         1016
#define _APS_NEXT_SYMED_VALUE           102
#endif
#endif
