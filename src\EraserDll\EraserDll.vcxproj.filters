﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{3ce290b6-1d0e-4517-b0c4-bdcc98d91537}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{2d9c3d34-1768-4094-a6de-9c8beee6e7e6}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{90af6995-ab64-423e-a59e-1a8897ecc75b}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;cnt;rtf;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ByteEdit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Common.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Custom.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CustomMethodEdit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DOD.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Eraser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FAT.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="File.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FileLockResolver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FillMemoryWith.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FirstLast2kb.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FreeSpace.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Gutmann.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NTFS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="OptionPages.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Options.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="OptionsDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Pass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PassEditDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Random.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ReportDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RND.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sboxes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Schneier7Pass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SecManDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SecurityManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tiger.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Eraser.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ByteEdit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Custom.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CustomMethodEdit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DOD.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Eraser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EraserDll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EraserDllInternal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EraserExport.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FAT.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="File.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FileLockResolver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FillMemoryWith.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FirstLast2kb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FreeSpace.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Gutmann.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NTFS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="OptionPages.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="options.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="OptionsDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Pass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PassEditDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Random.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ReportDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RND.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Schneier7pass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SecManDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SecurityManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Stack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tiger.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\Eraser.ico">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="..\res\Eraser.ico">
      <Filter>Resource Files</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\Eraser.rc2">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>