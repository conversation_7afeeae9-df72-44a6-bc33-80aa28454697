﻿// 静态库导出定义
// 当编译为静态库时，不需要 __declspec 修饰符
#ifdef _LIB_ERASER
// 静态库模式：直接导出函数，无需 dllexport/dllimport
#define ERASER_EXPORT LONG __stdcall
#define ERASER_API
#elif defined(_DLL_ERASER)
// 动态库模式：导出函数
#define ERASER_EXPORT __declspec(dllexport) LONG __stdcall
#define ERASER_API __declspec(dllexport)
#else
// 动态库模式：导入函数
#define ERASER_EXPORT __declspec(dllimport) LONG __stdcall
#define ERASER_API __declspec(dllimport)
#endif