@echo off
REM EraserLib 静态库编译测试脚本
REM 
REM 本脚本用于测试 EraserLib 静态库的编译过程
REM 支持多种配置和平台的编译测试

echo ========================================
echo EraserLib 静态库编译测试
echo ========================================
echo.

REM 设置 Visual Studio 环境
echo 正在设置 Visual Studio 开发环境...
call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat" >nul 2>&1
if errorlevel 1 (
    echo 错误: 无法找到 Visual Studio 2022 Enterprise
    echo 尝试查找其他版本...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" >nul 2>&1
    if errorlevel 1 (
        echo 错误: 无法找到 Visual Studio 开发环境
        echo 请确保已安装 Visual Studio 2019 或 2022
        pause
        exit /b 1
    )
)
echo Visual Studio 环境设置完成

echo.
echo ========================================
echo 开始编译测试
echo ========================================

REM 编译 Release Win32 配置
echo.
echo [1/4] 编译 Release^|Win32 配置...
msbuild EraserLib.vcxproj /p:Configuration=Release /p:Platform=Win32 /verbosity:minimal /nologo
if errorlevel 1 (
    echo 错误: Release^|Win32 编译失败
    goto :error
) else (
    echo 成功: Release^|Win32 编译完成
)

REM 编译 Debug Win32 配置
echo.
echo [2/4] 编译 Debug^|Win32 配置...
msbuild EraserLib.vcxproj /p:Configuration=Debug /p:Platform=Win32 /verbosity:minimal /nologo
if errorlevel 1 (
    echo 错误: Debug^|Win32 编译失败
    goto :error
) else (
    echo 成功: Debug^|Win32 编译完成
)

REM 编译 Release x64 配置
echo.
echo [3/4] 编译 Release^|x64 配置...
msbuild EraserLib.vcxproj /p:Configuration=Release /p:Platform=x64 /verbosity:minimal /nologo
if errorlevel 1 (
    echo 错误: Release^|x64 编译失败
    goto :error
) else (
    echo 成功: Release^|x64 编译完成
)

REM 编译 Debug x64 配置
echo.
echo [4/4] 编译 Debug^|x64 配置...
msbuild EraserLib.vcxproj /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal /nologo
if errorlevel 1 (
    echo 错误: Debug^|x64 编译失败
    goto :error
) else (
    echo 成功: Debug^|x64 编译完成
)

echo.
echo ========================================
echo 编译测试完成
echo ========================================

REM 检查输出文件
echo.
echo 检查生成的库文件:
echo.

set "found_files=0"

if exist "bin\Win32\Release\EraserLib.lib" (
    echo [✓] bin\Win32\Release\EraserLib.lib
    set /a found_files+=1
) else (
    echo [✗] bin\Win32\Release\EraserLib.lib - 未找到
)

if exist "bin\Win32\Debug\EraserLib.lib" (
    echo [✓] bin\Win32\Debug\EraserLib.lib
    set /a found_files+=1
) else (
    echo [✗] bin\Win32\Debug\EraserLib.lib - 未找到
)

if exist "bin\x64\Release\EraserLib.lib" (
    echo [✓] bin\x64\Release\EraserLib.lib
    set /a found_files+=1
) else (
    echo [✗] bin\x64\Release\EraserLib.lib - 未找到
)

if exist "bin\x64\Debug\EraserLib.lib" (
    echo [✓] bin\x64\Debug\EraserLib.lib
    set /a found_files+=1
) else (
    echo [✗] bin\x64\Debug\EraserLib.lib - 未找到
)

echo.
if %found_files% equ 4 (
    echo ✅ 所有配置编译成功！静态库转换完成。
    echo.
    echo 生成的库文件可以在以下位置找到:
    echo   - bin\Win32\Release\EraserLib.lib
    echo   - bin\Win32\Debug\EraserLib.lib  
    echo   - bin\x64\Release\EraserLib.lib
    echo   - bin\x64\Debug\EraserLib.lib
    echo.
    echo 您现在可以在项目中使用这些静态库文件。
    echo 请参阅 README_StaticLib.md 获取详细的使用说明。
) else (
    echo ⚠️  部分配置编译成功，但有 %found_files%/4 个库文件生成。
    echo 请检查编译输出以了解详细信息。
)

echo.
echo 按任意键退出...
pause >nul
exit /b 0

:error
echo.
echo ❌ 编译失败！
echo.
echo 可能的解决方案:
echo 1. 检查是否安装了正确版本的 Visual Studio
echo 2. 确保所有源文件都存在且没有语法错误
echo 3. 检查项目配置是否正确
echo 4. 查看详细的编译错误信息
echo.
echo 如需帮助，请查看 CONVERSION_SUMMARY.md 文件。
echo.
echo 按任意键退出...
pause >nul
exit /b 1
