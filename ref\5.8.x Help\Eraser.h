 
// Commands (ID_* and IDM_*) 
#define HID_GFX_ANIMATION                       0x1006E
#define HID_TREEFILECTRL_FORWARD                0x18002
#define HID_GFX_LARGEICON                       0x18003
#define HID_GFX_SMALLICON                       0x18004
#define HID_GFX_REMOVEITEM                      0x18005
#define HID_FILE_EXPORT                         0x18006
#define HID_GFX_RENAMEITEM                      0x18007
#define HID_FILE_IMPORT                         0x18008
#define HID_VIEW_INFO_BAR                       0x18009
#define HID_FILE_NEW_TASK                       0x1800A
#define HID_EDIT_PROPERTIES                     0x1800B
#define HID_EDIT_DELETE_TASK                    0x1800C
#define HID_PROCESS_RUN                         0x1800D
#define HID_PROCESS_STOP                        0x1800E
#define HID_EDIT_PREFERENCES_ERASER             0x1800F
#define HID_EDIT_PREFERENCES_GENERAL            0x18010
#define HID_TRAY_SHOW_WINDOW                    0x18011
#define HID_TRAY_ENABLE                         0x18012
#define HID_FILE_VIEW_LOG                       0x18013
#define HID_HELP_REWARD                         0x18014
#define HID_EDIT_REFRESH                        0x18015
#define HID_DRAG_MOVE                           0x18016
#define HID_DRAG_COPY                           0x18017
#define HID_DRAG_CANCEL                         0x18018
#define HID_TREEFILECTRL_UPONELEVEL             0x18019
#define HID_PROCESS_RUNALL                      0x18019
#define HID_TREEFILECTRL_OPEN                   0x1801A
#define HID_TREEFILECTRL_DELETE                 0x1801B
#define HID_TREEFILECTRL_RENAME                 0x1801C
#define HID_TREEFILECTRL_PROPERTIES             0x1801D
#define HID_TREEFILECTRL_REFRESH                0x1801E
#define HID_TREEFILECTRL_BACK                   0x1801F
#define HID_INDICATOR_ITEMS                     0x1E706
 
// Prompts (IDP_*) 
 
// Resources (IDR_*) 
#define HIDR_TREEFILECTRL_NO_DROPCOPY           0x2005E
#define HIDR_TREEFILECTRL_DROPCOPY              0x20060
#define HIDR_ERASEREXPLORER                     0x2006D
#define HIDR_TREEFILECTRL_POPUP                 0x2006F
#define HIDR_MAINFRAME                          0x20080
#define HIDR_ERASERTYPE                         0x20081
#define HIDR_GFXMENU_TOOLBAR                    0x20087
#define HIDR_MENU_ERASERVIEW                    0x20088
#define HIDR_MENU_SCHEDULERVIEW                 0x20089
#define HIDR_MENU_TRAY                          0x2008F
#define HIDR_CHILDFRAME                         0x20092
#define HIDR_MENU_RDROP                         0x20093
#define HIDR_RT_RCDATA1                         0x2009E
#define HIDR_RT_RCDATA2                         0x200A0
#define HIDR_TREEFILECTRL_NO_DROPMOVE           0x200C2
 
// Dialogs (IDD_*) 
#define HIDD_DLGNEW_DIALOG                      0x2005D
#define HIDD_ABOUTBOX                           0x20064
#define HIDD_PROPPAGE_ERASER                    0x20069
#define HIDD_PROPPAGE_SCHEDULER                 0x2006A
#define HIDD_PROPPAGE_TASKDATA                  0x2008A
#define HIDD_PROPPAGE_TASKSCHEDULE              0x2008B
#define HIDD_PROPPAGE_TASKSTATISTICS            0x2008C
#define HIDD_DIALOG_ERASER                      0x2008D
#define HIDD_DIALOG_HOTKEYS                     0x200AB
#define HIDD_DIALOG_KEYCOMBO                    0x200AE
 
// Frame Controls (IDW_*) 
#define HIDW_INFO_BAR                           0x5006B
