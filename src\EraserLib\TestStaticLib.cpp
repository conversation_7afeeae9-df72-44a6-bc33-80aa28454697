// TestStaticLib.cpp - EraserLib 静态库测试程序
//
// 本文件演示如何使用 EraserLib 静态库进行安全文件删除
// 这是一个简单的控制台应用程序，展示了基本的API使用方法

#include "stdafx.h"
#include "EraserDll.h"
#include <iostream>
#include <string>
#include <windows.h>

using namespace std;

// ============================================================================
// 辅助函数
// ============================================================================

/**
 * 将错误码转换为可读的错误信息
 */
const char* GetErrorString(ERASER_RESULT error)
{
    switch (error) {
    case ERASER_OK: return "成功";
    case ERASER_ERROR: return "未指定错误";
    case ERASER_ERROR_PARAM1: return "参数1无效";
    case ERASER_ERROR_PARAM2: return "参数2无效";
    case ERASER_ERROR_PARAM3: return "参数3无效";
    case ERASER_ERROR_PARAM4: return "参数4无效";
    case ERASER_ERROR_PARAM5: return "参数5无效";
    case ERASER_ERROR_PARAM6: return "参数6无效";
    case ERASER_ERROR_MEMORY: return "内存不足";
    case ERASER_ERROR_THREAD: return "线程启动失败";
    case ERASER_ERROR_EXCEPTION: return "异常错误";
    case ERASER_ERROR_CONTEXT: return "上下文数组已满";
    case ERASER_ERROR_INIT: return "库未初始化";
    case ERASER_ERROR_RUNNING: return "操作正在运行";
    case ERASER_ERROR_NOTRUNNING: return "操作未运行";
    case ERASER_ERROR_DENIED: return "权限不足";
    case ERASER_ERROR_NOTIMPLEMENTED: return "功能未实现";
    default: return "未知错误";
    }
}

/**
 * 创建测试文件
 */
bool CreateTestFile(const string& filename, const string& content)
{
    HANDLE hFile = CreateFileA(filename.c_str(), GENERIC_WRITE, 0, NULL, 
                              CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile == INVALID_HANDLE_VALUE) {
        cout << "无法创建测试文件: " << filename << endl;
        return false;
    }
    
    DWORD written;
    bool success = WriteFile(hFile, content.c_str(), content.length(), &written, NULL);
    CloseHandle(hFile);
    
    if (!success || written != content.length()) {
        cout << "写入测试文件失败: " << filename << endl;
        return false;
    }
    
    cout << "创建测试文件成功: " << filename << " (大小: " << content.length() << " 字节)" << endl;
    return true;
}

/**
 * 检查文件是否存在
 */
bool FileExists(const string& filename)
{
    DWORD attr = GetFileAttributesA(filename.c_str());
    return (attr != INVALID_FILE_ATTRIBUTES && !(attr & FILE_ATTRIBUTE_DIRECTORY));
}

// ============================================================================
// 测试函数
// ============================================================================

/**
 * 测试1：基本库初始化和清理
 */
bool TestLibraryInit()
{
    cout << "\n=== 测试1：库初始化和清理 ===" << endl;
    
    ERASER_RESULT result = eraserInit();
    if (eraserError(result)) {
        cout << "库初始化失败: " << GetErrorString(result) << endl;
        return false;
    }
    cout << "库初始化成功" << endl;
    
    result = eraserEnd();
    if (eraserError(result)) {
        cout << "库清理失败: " << GetErrorString(result) << endl;
        return false;
    }
    cout << "库清理成功" << endl;
    
    return true;
}

/**
 * 测试2：上下文创建和销毁
 */
bool TestContextManagement()
{
    cout << "\n=== 测试2：上下文管理 ===" << endl;
    
    ERASER_RESULT result = eraserInit();
    if (eraserError(result)) {
        cout << "库初始化失败: " << GetErrorString(result) << endl;
        return false;
    }
    
    ERASER_HANDLE handle;
    result = eraserCreateContext(&handle);
    if (eraserError(result)) {
        cout << "创建上下文失败: " << GetErrorString(result) << endl;
        eraserEnd();
        return false;
    }
    cout << "创建上下文成功，句柄: " << handle << endl;
    
    // 验证上下文有效性
    result = eraserIsValidContext(handle);
    if (eraserError(result)) {
        cout << "上下文验证失败: " << GetErrorString(result) << endl;
        eraserEnd();
        return false;
    }
    cout << "上下文验证成功" << endl;
    
    // 销毁上下文
    result = eraserDestroyContext(handle);
    if (eraserError(result)) {
        cout << "销毁上下文失败: " << GetErrorString(result) << endl;
        eraserEnd();
        return false;
    }
    cout << "销毁上下文成功" << endl;
    
    eraserEnd();
    return true;
}

/**
 * 测试3：简单文件擦除
 */
bool TestSimpleFileErase()
{
    cout << "\n=== 测试3：简单文件擦除 ===" << endl;
    
    // 创建测试文件
    string testFile = "test_erase.txt";
    string testContent = "这是一个包含敏感信息的测试文件。\n"
                        "文件内容将被安全擦除。\n"
                        "123456789ABCDEF\n";
    
    if (!CreateTestFile(testFile, testContent)) {
        return false;
    }
    
    // 初始化库
    ERASER_RESULT result = eraserInit();
    if (eraserError(result)) {
        cout << "库初始化失败: " << GetErrorString(result) << endl;
        return false;
    }
    
    // 创建上下文
    ERASER_HANDLE handle;
    result = eraserCreateContext(&handle);
    if (eraserError(result)) {
        cout << "创建上下文失败: " << GetErrorString(result) << endl;
        eraserEnd();
        return false;
    }
    
    // 设置数据类型为文件
    result = eraserSetDataType(handle, ERASER_DATA_FILES);
    if (eraserError(result)) {
        cout << "设置数据类型失败: " << GetErrorString(result) << endl;
        eraserDestroyContext(handle);
        eraserEnd();
        return false;
    }
    cout << "设置数据类型为文件成功" << endl;
    
    // 添加要擦除的文件
    result = eraserAddItem(handle, (LPVOID)testFile.c_str(), testFile.length());
    if (eraserError(result)) {
        cout << "添加文件失败: " << GetErrorString(result) << endl;
        eraserDestroyContext(handle);
        eraserEnd();
        return false;
    }
    cout << "添加文件成功: " << testFile << endl;
    
    // 开始同步擦除
    cout << "开始擦除文件..." << endl;
    result = eraserStartSync(handle);
    if (eraserError(result)) {
        cout << "擦除失败: " << GetErrorString(result) << endl;
        eraserDestroyContext(handle);
        eraserEnd();
        return false;
    }
    
    // 检查完成状态
    E_UINT8 completed;
    result = eraserCompleted(handle, &completed);
    if (eraserError(result)) {
        cout << "检查完成状态失败: " << GetErrorString(result) << endl;
    } else if (completed) {
        cout << "文件擦除完成" << endl;
    } else {
        cout << "文件擦除未完成" << endl;
    }
    
    // 验证文件是否被删除
    if (FileExists(testFile)) {
        cout << "警告: 文件仍然存在，擦除可能未成功" << endl;
    } else {
        cout << "确认: 文件已被成功删除" << endl;
    }
    
    // 获取统计信息
    E_UINT64 wiped;
    if (eraserOK(eraserStatGetWiped(handle, &wiped))) {
        cout << "擦除数据量: " << wiped << " 字节" << endl;
    }
    
    E_UINT32 time;
    if (eraserOK(eraserStatGetTime(handle, &time))) {
        cout << "擦除耗时: " << time << " 毫秒" << endl;
    }
    
    // 清理
    eraserDestroyContext(handle);
    eraserEnd();
    
    return true;
}

// ============================================================================
// 主函数
// ============================================================================

int main()
{
    cout << "EraserLib 静态库测试程序" << endl;
    cout << "========================" << endl;
    
    bool allTestsPassed = true;
    
    // 运行所有测试
    allTestsPassed &= TestLibraryInit();
    allTestsPassed &= TestContextManagement();
    allTestsPassed &= TestSimpleFileErase();
    
    // 输出测试结果
    cout << "\n========================" << endl;
    if (allTestsPassed) {
        cout << "所有测试通过！静态库工作正常。" << endl;
    } else {
        cout << "部分测试失败！请检查静态库配置。" << endl;
    }
    
    cout << "\n按任意键退出..." << endl;
    cin.get();
    
    return allTestsPassed ? 0 : 1;
}
