﻿#ifndef ERASER_H
#define ERASER_H

#ifndef __AFXWIN_H__
    #error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"       // main symbols


/////////////////////////////////////////////////////////////////////////////
// CEraserDll
// See Eraser.cpp for the implementation of this class
//

class CEraserDll : public CWinApp
{
public:
    CEraserDll();

// Overrides
    // ClassWizard generated virtual function overrides
    //{{AFX_VIRTUAL(CEraserApp)
	public:
    virtual BOOL InitInstance();
	virtual int ExitInstance();
	//}}AFX_VIRTUAL

    //{{AFX_MSG(CEraserApp)
        // NOTE - the ClassWizard will add and remove member functions here.
        //    DO NOT EDIT what you see in these blocks of generated code !
    //}}AFX_MSG
    DECLARE_MESSAGE_MAP()
};
/////////////////////////////////////////////////////////////////////////////

#endif
