Microsoft Visual Studio Solution File, Format Version 10.00
# Visual Studio 2008
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Eraser", "Eraser.vcproj", "{EC74DAF8-5106-470B-A8F4-D539B347E20E}"
	ProjectSection(ProjectDependencies) = postProject
		{870168AC-012D-4E78-AB70-CC20D02C0EBE} = {870168AC-012D-4E78-AB70-CC20D02C0EBE}
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05} = {D4750DE4-A865-494B-B5AD-84DF59ADFC05}
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4} = {B4BCD9DD-A614-486E-B168-4D2B4820F2B4}
		{31F52DA7-B9F6-452D-823F-E1EFDF283995} = {31F52DA7-B9F6-452D-823F-E1EFDF283995}
		{E46B0C12-7663-4E53-BD88-117C49328CA9} = {E46B0C12-7663-4E53-BD88-117C49328CA9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "EraserDll", "EraserDll\EraserDll.vcproj", "{31F52DA7-B9F6-452D-823F-E1EFDF283995}"
	ProjectSection(ProjectDependencies) = postProject
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4} = {B4BCD9DD-A614-486E-B168-4D2B4820F2B4}
		{E46B0C12-7663-4E53-BD88-117C49328CA9} = {E46B0C12-7663-4E53-BD88-117C49328CA9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Erasext", "Erasext\Erasext.vcproj", "{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}"
	ProjectSection(ProjectDependencies) = postProject
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4} = {B4BCD9DD-A614-486E-B168-4D2B4820F2B4}
		{31F52DA7-B9F6-452D-823F-E1EFDF283995} = {31F52DA7-B9F6-452D-823F-E1EFDF283995}
		{E46B0C12-7663-4E53-BD88-117C49328CA9} = {E46B0C12-7663-4E53-BD88-117C49328CA9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Launcher", "Launcher\Launcher.vcproj", "{D4750DE4-A865-494B-B5AD-84DF59ADFC05}"
	ProjectSection(ProjectDependencies) = postProject
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4} = {B4BCD9DD-A614-486E-B168-4D2B4820F2B4}
		{31F52DA7-B9F6-452D-823F-E1EFDF283995} = {31F52DA7-B9F6-452D-823F-E1EFDF283995}
		{E46B0C12-7663-4E53-BD88-117C49328CA9} = {E46B0C12-7663-4E53-BD88-117C49328CA9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Verify", "Verify\Verify.vcproj", "{870168AC-012D-4E78-AB70-CC20D02C0EBE}"
	ProjectSection(ProjectDependencies) = postProject
		{31F52DA7-B9F6-452D-823F-E1EFDF283995} = {31F52DA7-B9F6-452D-823F-E1EFDF283995}
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4} = {B4BCD9DD-A614-486E-B168-4D2B4820F2B4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "EraserUI", "EraserUI\EraserUI.vcproj", "{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Shared", "shared\Shared.vcproj", "{E46B0C12-7663-4E53-BD88-117C49328CA9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug_Unicode|Win32 = Debug_Unicode|Win32
		Debug_Unicode|x64 = Debug_Unicode|x64
		Debug|Win32 = Debug|Win32
		Debug|x64 = Debug|x64
		Release_Unicode|Win32 = Release_Unicode|Win32
		Release_Unicode|x64 = Release_Unicode|x64
		Release|Win32 = Release|Win32
		Release|x64 = Release|x64
		Standalone Release Unicode|Win32 = Standalone Release Unicode|Win32
		Standalone Release Unicode|x64 = Standalone Release Unicode|x64
		Standalone Release|Win32 = Standalone Release|Win32
		Standalone Release|x64 = Standalone Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Debug_Unicode|Win32.ActiveCfg = Debug_Unicode|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Debug_Unicode|Win32.Build.0 = Debug_Unicode|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Debug_Unicode|x64.ActiveCfg = Debug_Unicode|x64
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Debug_Unicode|x64.Build.0 = Debug_Unicode|x64
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Debug|Win32.ActiveCfg = Debug|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Debug|Win32.Build.0 = Debug|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Debug|x64.ActiveCfg = Debug|x64
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Release_Unicode|Win32.ActiveCfg = Release_Unicode|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Release_Unicode|Win32.Build.0 = Release_Unicode|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Release_Unicode|x64.ActiveCfg = Release_Unicode|x64
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Release_Unicode|x64.Build.0 = Release_Unicode|x64
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Release|Win32.ActiveCfg = Release|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Release|Win32.Build.0 = Release|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Release|x64.ActiveCfg = Release|x64
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Standalone Release Unicode|Win32.ActiveCfg = Standalone Release Unicode|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Standalone Release Unicode|Win32.Build.0 = Standalone Release Unicode|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Standalone Release Unicode|x64.ActiveCfg = Standalone Release Unicode|x64
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Standalone Release Unicode|x64.Build.0 = Standalone Release Unicode|x64
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Standalone Release|Win32.ActiveCfg = Standalone Release|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Standalone Release|Win32.Build.0 = Standalone Release|Win32
		{EC74DAF8-5106-470B-A8F4-D539B347E20E}.Standalone Release|x64.ActiveCfg = Standalone Release|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Debug_Unicode|Win32.ActiveCfg = Debug_Unicode|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Debug_Unicode|Win32.Build.0 = Debug_Unicode|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Debug_Unicode|x64.ActiveCfg = Debug_Unicode|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Debug_Unicode|x64.Build.0 = Debug_Unicode|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Debug|Win32.ActiveCfg = Debug|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Debug|Win32.Build.0 = Debug|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Debug|x64.ActiveCfg = Debug|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Release_Unicode|Win32.ActiveCfg = Release_Unicode|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Release_Unicode|Win32.Build.0 = Release_Unicode|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Release_Unicode|x64.ActiveCfg = Release_Unicode|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Release_Unicode|x64.Build.0 = Release_Unicode|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Release|Win32.ActiveCfg = Release|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Release|Win32.Build.0 = Release|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Release|x64.ActiveCfg = Release|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Standalone Release Unicode|Win32.ActiveCfg = Standalone Release Unicode|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Standalone Release Unicode|Win32.Build.0 = Standalone Release Unicode|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Standalone Release Unicode|x64.ActiveCfg = Standalone Release Unicode|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Standalone Release Unicode|x64.Build.0 = Standalone Release Unicode|x64
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Standalone Release|Win32.ActiveCfg = Standalone Release|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Standalone Release|Win32.Build.0 = Standalone Release|Win32
		{31F52DA7-B9F6-452D-823F-E1EFDF283995}.Standalone Release|x64.ActiveCfg = Standalone Release|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Debug_Unicode|Win32.ActiveCfg = Debug_Unicode|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Debug_Unicode|Win32.Build.0 = Debug_Unicode|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Debug_Unicode|x64.ActiveCfg = Debug_Unicode|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Debug_Unicode|x64.Build.0 = Debug_Unicode|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Debug|Win32.ActiveCfg = Debug|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Debug|Win32.Build.0 = Debug|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Debug|x64.ActiveCfg = Debug|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Release_Unicode|Win32.ActiveCfg = Release_Unicode|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Release_Unicode|Win32.Build.0 = Release_Unicode|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Release_Unicode|x64.ActiveCfg = Release_Unicode|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Release_Unicode|x64.Build.0 = Release_Unicode|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Release|Win32.ActiveCfg = Release|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Release|Win32.Build.0 = Release|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Release|x64.ActiveCfg = Release|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Standalone Release Unicode|Win32.ActiveCfg = Standalone Release Unicode|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Standalone Release Unicode|x64.ActiveCfg = Standalone Release Unicode|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Standalone Release Unicode|x64.Build.0 = Standalone Release Unicode|x64
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Standalone Release|Win32.ActiveCfg = Standalone Release|Win32
		{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}.Standalone Release|x64.ActiveCfg = Standalone Release|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Debug_Unicode|Win32.ActiveCfg = Debug_Unicode|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Debug_Unicode|Win32.Build.0 = Debug_Unicode|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Debug_Unicode|x64.ActiveCfg = Debug_Unicode|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Debug_Unicode|x64.Build.0 = Debug_Unicode|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Debug|Win32.ActiveCfg = Debug|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Debug|Win32.Build.0 = Debug|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Debug|x64.ActiveCfg = Debug|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Release_Unicode|Win32.ActiveCfg = Release_Unicode|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Release_Unicode|Win32.Build.0 = Release_Unicode|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Release_Unicode|x64.ActiveCfg = Release_Unicode|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Release_Unicode|x64.Build.0 = Release_Unicode|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Release|Win32.ActiveCfg = Release|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Release|Win32.Build.0 = Release|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Release|x64.ActiveCfg = Release|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Standalone Release Unicode|Win32.ActiveCfg = Standalone Release Unicode|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Standalone Release Unicode|Win32.Build.0 = Standalone Release Unicode|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Standalone Release Unicode|x64.ActiveCfg = Standalone Release Unicode|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Standalone Release Unicode|x64.Build.0 = Standalone Release Unicode|x64
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Standalone Release|Win32.ActiveCfg = Standalone Release|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Standalone Release|Win32.Build.0 = Standalone Release|Win32
		{D4750DE4-A865-494B-B5AD-84DF59ADFC05}.Standalone Release|x64.ActiveCfg = Standalone Release|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Debug_Unicode|Win32.ActiveCfg = Debug_Unicode|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Debug_Unicode|Win32.Build.0 = Debug_Unicode|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Debug_Unicode|x64.ActiveCfg = Debug_Unicode|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Debug_Unicode|x64.Build.0 = Debug_Unicode|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Debug|Win32.ActiveCfg = Debug|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Debug|Win32.Build.0 = Debug|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Debug|x64.ActiveCfg = Debug|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Release_Unicode|Win32.ActiveCfg = Release_Unicode|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Release_Unicode|Win32.Build.0 = Release_Unicode|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Release_Unicode|x64.ActiveCfg = Release_Unicode|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Release_Unicode|x64.Build.0 = Release_Unicode|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Release|Win32.ActiveCfg = Release|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Release|Win32.Build.0 = Release|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Release|x64.ActiveCfg = Release|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Standalone Release Unicode|Win32.ActiveCfg = Standalone Release Unicode|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Standalone Release Unicode|x64.ActiveCfg = Standalone Release Unicode|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Standalone Release Unicode|x64.Build.0 = Standalone Release Unicode|x64
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Standalone Release|Win32.ActiveCfg = Standalone Release|Win32
		{870168AC-012D-4E78-AB70-CC20D02C0EBE}.Standalone Release|x64.ActiveCfg = Standalone Release|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Debug_Unicode|Win32.ActiveCfg = Debug_Unicode|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Debug_Unicode|Win32.Build.0 = Debug_Unicode|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Debug_Unicode|x64.ActiveCfg = Debug_Unicode|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Debug_Unicode|x64.Build.0 = Debug_Unicode|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Debug|Win32.ActiveCfg = Debug|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Debug|Win32.Build.0 = Debug|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Debug|x64.ActiveCfg = Debug|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Release_Unicode|Win32.ActiveCfg = Release_Unicode|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Release_Unicode|Win32.Build.0 = Release_Unicode|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Release_Unicode|x64.ActiveCfg = Release_Unicode|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Release_Unicode|x64.Build.0 = Release_Unicode|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Release|Win32.ActiveCfg = Release|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Release|Win32.Build.0 = Release|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Release|x64.ActiveCfg = Release|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Standalone Release Unicode|Win32.ActiveCfg = Standalone Release Unicode|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Standalone Release Unicode|Win32.Build.0 = Standalone Release Unicode|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Standalone Release Unicode|x64.ActiveCfg = Standalone Release Unicode|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Standalone Release Unicode|x64.Build.0 = Standalone Release Unicode|x64
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Standalone Release|Win32.ActiveCfg = Standalone Release|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Standalone Release|Win32.Build.0 = Standalone Release|Win32
		{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}.Standalone Release|x64.ActiveCfg = Standalone Release|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Debug_Unicode|Win32.ActiveCfg = Debug_Unicode|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Debug_Unicode|Win32.Build.0 = Debug_Unicode|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Debug_Unicode|x64.ActiveCfg = Debug_Unicode|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Debug_Unicode|x64.Build.0 = Debug_Unicode|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Debug|Win32.ActiveCfg = Debug|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Debug|Win32.Build.0 = Debug|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Debug|x64.ActiveCfg = Debug|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Release_Unicode|Win32.ActiveCfg = Release_Unicode|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Release_Unicode|Win32.Build.0 = Release_Unicode|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Release_Unicode|x64.ActiveCfg = Release_Unicode|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Release_Unicode|x64.Build.0 = Release_Unicode|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Release|Win32.ActiveCfg = Release|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Release|Win32.Build.0 = Release|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Release|x64.ActiveCfg = Release|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Standalone Release Unicode|Win32.ActiveCfg = Standalone Release Unicode|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Standalone Release Unicode|Win32.Build.0 = Standalone Release Unicode|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Standalone Release Unicode|x64.ActiveCfg = Standalone Release Unicode|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Standalone Release Unicode|x64.Build.0 = Standalone Release Unicode|x64
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Standalone Release|Win32.ActiveCfg = Standalone Release|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Standalone Release|Win32.Build.0 = Standalone Release|Win32
		{E46B0C12-7663-4E53-BD88-117C49328CA9}.Standalone Release|x64.ActiveCfg = Standalone Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(SubversionScc) = preSolution
		Svn-Managed = True
		Manager = AnkhSVN - Subversion Support for Visual Studio
	EndGlobalSection
EndGlobal
