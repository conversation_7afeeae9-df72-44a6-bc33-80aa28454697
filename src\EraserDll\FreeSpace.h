﻿#ifndef FREESPACE_H
#define FREESPACE_H

void
countFilesOnDrive(CEraserContext *context, const CString& strDrive, E_UINT32& uFiles, E_UINT32& uFolders);
bool
getClusterSize(LPCTSTR szDrive, E_UINT32& uCluster);
bool
getClusterAndSectorSize(LPCTSTR szDrive, E_UINT32& uCluster, E_UINT32& uSector);

bool
getPartitionType(PARTITIONINFO& pi);
bool
getPartitionInformation(CEraserContext *context, TCHAR cDrive);

bool
wipeMFTRecords(CEraserContext *context);
bool
wipeClusterTips(CEraserContext *context);
bool
wipeFreeSpace(CEraserContext *context);

#endif
