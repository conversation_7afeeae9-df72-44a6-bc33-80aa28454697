﻿// NTFS.cpp - NTFS文件系统相关的安全擦除功能实现
//
// 本文件实现了针对NTFS文件系统的特殊擦除功能，包括：
// 1. 压缩文件的擦除
// 2. MFT记录的擦除
// 3. 文件目录项的擦除
// 4. 备用数据流的查找和处理

#include "stdafx.h"
#include "resource.h"
#include "EraserDll.h"
#include "Common.h"
#include "File.h"
#include "NTFS.h"
#include "winioctl.h"

// 无效的长长整型数值标识符
// 在NTFS中，-1表示虚拟簇号无效（通常用于压缩文件的空洞部分）
#define LLINVALID       ((E_UINT64) -1)

// 文件映射信息缓冲区大小
// 缓冲区足够大，可以容纳：
// - 16字节的头部信息（条目数量和起始虚拟簇号）
// - 512对[虚拟簇号, 逻辑簇号]映射对
// 每个映射对占用16字节（两个8字节的簇号）
#define FILEMAPSIZE     (16384 + 2)


/**
 * 初始化NTFS操作所需的系统API入口点
 *
 * @param ntc NTFS上下文结构，用于存储函数指针和句柄
 * @return 成功返回true，失败返回false
 *
 * 功能说明：
 * - 动态加载ntdll.dll库
 * - 获取NtFsControlFile、NtQueryInformationFile、RtlNtStatusToDosError函数地址
 * - 这些是未公开的NT内核API，用于直接操作文件系统
 */
static bool
initEntryPoints(NTFSContext& ntc)
{
    // 加载NTDLL.DLL库，获取我们需要的NT内核API入口点
    ntc.m_hNTDLL = AfxLoadLibrary(ERASER_MODULENAME_NTDLL);

    if (ntc.m_hNTDLL != NULL) {
        // 获取NtFsControlFile函数地址 - 用于发送文件系统控制命令
        ntc.NtFsControlFile =
            (NTFSCONTROLFILE) GetProcAddress(ntc.m_hNTDLL, ERASER_FUNCTIONNAME_NTFSCONTROLFILE);

        // 获取NtQueryInformationFile函数地址 - 用于查询文件信息
        ntc.NtQueryInformationFile =
            (NTQUERYINFORMATIONFILE) GetProcAddress(ntc.m_hNTDLL, ERASER_FUNCTIONNAME_NTQUERYINFORMATIONFILE);

        // 获取RtlNtStatusToDosError函数地址 - 用于将NT状态码转换为DOS错误码
        ntc.RtlNtStatusToDosError =
            (RTLNTSTATUSTODOSERROR) GetProcAddress(ntc.m_hNTDLL, ERASER_FUNCTIONNAME_RTLNTSTATUSTODOSERROR);

        // 如果关键函数获取失败，释放库并设置句柄为NULL
        if (ntc.NtFsControlFile == NULL || ntc.RtlNtStatusToDosError == NULL) {
            AfxFreeLibrary(ntc.m_hNTDLL);
            ntc.m_hNTDLL = NULL;
        }
    }

    return (ntc.m_hNTDLL != NULL);
}

/**
 * 格式化NT状态码为可读的错误消息
 *
 * @param ntc NTFS上下文结构
 * @param dwStatus NT状态码
 * @return 格式化后的错误消息字符串
 *
 * 功能说明：
 * - 将NT内核状态码转换为DOS错误码
 * - 使用系统API格式化为用户可读的错误消息
 */
static CString
formatNTError(NTFSContext& ntc, NTSTATUS dwStatus)
{
    CString strMessage;
    LPTSTR szMessage;

    // 使用FormatMessage将错误码转换为可读的错误消息
    // FORMAT_MESSAGE_ALLOCATE_BUFFER: 系统自动分配消息缓冲区
    // FORMAT_MESSAGE_FROM_SYSTEM: 从系统消息表中查找消息
    FormatMessage(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM,
                  NULL,
                  ntc.RtlNtStatusToDosError(dwStatus),  // 将NT状态码转换为DOS错误码
                  MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),  // 使用默认语言
                  (LPTSTR)&szMessage, 0, NULL);

    strMessage = szMessage;
    LocalFree(szMessage);  // 释放系统分配的消息缓冲区

    return strMessage;
}

/**
 * 擦除文件的磁盘簇
 *
 * @param ntc NTFS上下文结构
 * @param context 擦除上下文
 * @param bCompressed 输出参数，指示文件是否为压缩文件
 * @return 成功返回true，失败返回false
 *
 * 功能说明：
 * - 获取文件的簇映射信息（虚拟簇号到逻辑簇号的映射）
 * - 对每个有效的逻辑簇进行安全擦除
 * - 处理压缩文件的特殊情况（压缩文件可能有"空洞"）
 */
static bool
wipeClusters(NTFSContext& ntc, CEraserContext *context, bool& bCompressed)
{
    // 检查簇大小是否有效
    if (context->m_piCurrent.m_uCluster == 0) {
        return false;
    }

    NTSTATUS                  status = STATUS_INVALID_PARAMETER;
    E_INT32                   i;
    IO_STATUS_BLOCK           ioStatus;           // I/O状态块
    E_UINT64                  startVcn;           // 起始虚拟簇号
    PGET_RETRIEVAL_DESCRIPTOR fileMappings;       // 文件映射描述符指针
    E_UINT64                  fileMap[FILEMAPSIZE]; // 文件映射缓冲区
    HANDLE                    hFile;

    // 保存原始文件句柄，并设置为卷句柄（用于直接访问磁盘簇）
    hFile = context->m_hFile;
    context->m_hFile = ntc.m_hVolume;

    // 假设文件存储在MFT记录中（小文件可能完全存储在MFT中）
    bCompressed = false;

    startVcn = 0;  // 从第0个虚拟簇开始
    fileMappings = (PGET_RETRIEVAL_DESCRIPTOR) fileMap;

    // 发送FSCTL_GET_RETRIEVAL_POINTERS控制命令获取文件的簇映射信息
    // 这个命令返回文件的虚拟簇号(VCN)到逻辑簇号(LCN)的映射关系
    status = ntc.NtFsControlFile(hFile, NULL, NULL, 0, &ioStatus,
                              FSCTL_GET_RETRIEVAL_POINTERS,  // 获取检索指针
                              &startVcn, sizeof(startVcn),   // 输入：起始虚拟簇号
                              fileMappings,                  // 输出：映射信息缓冲区
                              FILEMAPSIZE * sizeof(ULONGLONG));

    // 循环处理所有的簇映射信息
    // 可能需要多次调用，因为缓冲区可能不够大（STATUS_BUFFER_OVERFLOW）
    while (status == STATUS_SUCCESS || status == STATUS_BUFFER_OVERFLOW ||
           status == STATUS_PENDING) {

        // 如果操作处于挂起状态，等待其完成
        if (status == STATUS_PENDING) {
            WaitForSingleObject(hFile, INFINITE);

            // 从状态块获取最终状态
            if (ioStatus.Status != STATUS_SUCCESS &&
                ioStatus.Status != STATUS_BUFFER_OVERFLOW) {
                context->m_hFile = hFile;  // 恢复原始文件句柄
                return false;
            }
        }

        // 重置进度信息
        context->m_uProgressSize = 0;

        startVcn = fileMappings->StartVcn;  // 获取起始虚拟簇号

        // 第一遍遍历：计算总的需要擦除的数据大小（用于进度显示）
        for (i = 0; i < (E_UINT64) fileMappings->NumberOfPairs; i++) {
            // 只有当逻辑簇号有效时才计算大小
            // LLINVALID表示这是压缩文件的"空洞"部分，不占用实际磁盘空间
            if (fileMappings->Pair[i].Lcn != LLINVALID) {
                context->m_uProgressSize += (fileMappings->Pair[i].Vcn - startVcn) *
                                            (E_UINT64)context->m_piCurrent.m_uCluster;
            }

            startVcn = fileMappings->Pair[i].Vcn;  // 更新起始虚拟簇号
        }

        // 设置进度估算
        eraserProgressStartEstimate(context, context->m_uProgressSize);

        // 第二遍遍历：实际执行簇的擦除操作
        startVcn = fileMappings->StartVcn;

        for (i = 0; i < (E_UINT64)fileMappings->NumberOfPairs; i++) {
            // 在NT 4.0中，压缩文件的虚拟运行段（0填充部分）
            // 用簇偏移量-1来标识，表示这部分不占用实际磁盘空间

            if (fileMappings->Pair[i].Lcn != LLINVALID) {
                // 发现有效的逻辑簇，说明文件是压缩的且在MFT区域之外
                bCompressed = true;

                // 如果我们能够以写访问权限打开卷，就覆写这些簇
                // 计算要擦除的磁盘区域的起始位置（字节偏移）
                context->m_uiFileStart.QuadPart = fileMappings->Pair[i].Lcn * context->m_piCurrent.m_uCluster;
                // 计算要擦除的数据大小（字节数）
                context->m_uiFileSize.QuadPart = (fileMappings->Pair[i].Vcn - startVcn) *
                                                 (E_UINT64)context->m_piCurrent.m_uCluster;

                // 检查是否使用了"首末2KB擦除"方法
                // 对于压缩、加密或稀疏文件，这种方法不适用，因为可能导致磁盘损坏
				if (context->m_lpmMethod->m_pwfFunction == bmMethods[4].m_pwfFunction)
				{
					context->m_hFile = hFile;  // 恢复原始文件句柄
					context->m_saError.Add(_T("The file could not be erased with the first/last ")
						_T("2kb erasure because the file is compressed, encrypted or a sparse file."));
					return false;
				}

                // 调用指定的擦除方法来擦除这个簇范围
				if (!context->m_lpmMethod->m_pwfFunction(context)) {
                    context->m_hFile = hFile;  // 恢复原始文件句柄
                    return false;
                }

            }

            startVcn = fileMappings->Pair[i].Vcn;  // 更新起始虚拟簇号
        }

        // 如果缓冲区没有溢出，说明所有映射信息都已处理完毕
        if (NT_SUCCESS(status)) {
            break;
        }

        // 缓冲区溢出，需要继续获取剩余的映射信息
        // 从上次结束的虚拟簇号开始继续获取
        status = ntc.NtFsControlFile(hFile, NULL, NULL, 0, &ioStatus,
                                  FSCTL_GET_RETRIEVAL_POINTERS,
                                  &startVcn, sizeof(startVcn),
                                  fileMappings,
                                  FILEMAPSIZE * sizeof(E_UINT64));
    }

    // 检查最终状态，如果不是成功、无效参数或文件结束，则记录错误
    if (status != STATUS_SUCCESS && status != STATUS_INVALID_PARAMETER &&
		ntc.RtlNtStatusToDosError(status) != ERROR_HANDLE_EOF) {
        context->m_saError.Add(formatNTError(ntc, status));
    }

    // 恢复原始文件句柄
    context->m_hFile = hFile;

    // 如果没有错误或者只是到达文件末尾，说明成功覆写了文件的所有簇
    return NT_SUCCESS(status) || ntc.RtlNtStatusToDosError(status) == ERROR_HANDLE_EOF;
}

/**
 * 初始化NTFS上下文并打开指定驱动器的卷句柄
 *
 * @param ntc NTFS上下文结构
 * @param cDrive 驱动器字母（如'C'）
 * @return 成功返回true，失败返回false
 *
 * 功能说明：
 * - 初始化NT API入口点
 * - 打开指定驱动器的卷句柄，用于直接访问磁盘
 * - 需要管理员权限才能成功打开卷句柄
 */
static bool
initAndOpenVolume(NTFSContext& ntc, TCHAR cDrive)
{
    TCHAR szVolumeName[] = _T("\\\\.\\ :");  // 卷设备名称模板

    if (initEntryPoints(ntc)) {
        // 构造卷设备名称，如"\\.\C:"
        szVolumeName[4] = cDrive;

        // 打开卷进行直接访问
        // GENERIC_READ | GENERIC_WRITE: 需要读写权限
        // FILE_SHARE_READ | FILE_SHARE_WRITE: 允许其他进程共享访问
        // FILE_FLAG_WRITE_THROUGH: 绕过系统缓存，直接写入磁盘
        ntc.m_hVolume = CreateFile(szVolumeName, GENERIC_READ | GENERIC_WRITE,
                                   FILE_SHARE_READ | FILE_SHARE_WRITE, NULL,
                                   OPEN_EXISTING, FILE_FLAG_WRITE_THROUGH, 0);

        return (ntc.m_hVolume != INVALID_HANDLE_VALUE);
    }
    return false;
}

// ============================================================================
// 导出函数 - 这些函数可以被其他模块调用
// ============================================================================

/**
 * 擦除压缩文件
 *
 * @param context 擦除上下文
 * @return 擦除结果状态码：
 *         WCF_SUCCESS - 成功擦除压缩文件
 *         WCF_NOTCOMPRESSED - 文件不是压缩文件，应使用常规擦除方法
 *         WCF_FAILURE - 擦除失败
 *         WCF_NOACCESS - 没有访问权限
 *
 * 功能说明：
 * - 专门用于处理NTFS压缩文件的擦除
 * - 压缩文件的数据可能分散在磁盘的不同位置
 * - 需要直接访问磁盘簇来确保彻底擦除
 */
E_UINT32
wipeCompressedFile(CEraserContext *context)
{
    // 检查是否为NTFS文件系统
    if (!isFileSystemNTFS(context->m_piCurrent)) {
        return WCF_NOTCOMPRESSED;
    }

    NTFSContext ntc;                    // NTFS操作上下文
    E_UINT32 uResult = WCF_FAILURE;     // 默认返回失败
    bool bCompressed = false;           // 文件是否真的是压缩文件

    // 初始化NTFS上下文并打开卷句柄
    if (initAndOpenVolume(ntc, context->m_strData[0])) {
        // 打开文件进行独占访问
        // 在测试模式下允许共享访问，否则独占访问
        context->m_hFile = CreateFile((LPCTSTR)context->m_strData, GENERIC_READ,
                                      (context->m_uTestMode) ?
                                        FILE_SHARE_READ | FILE_SHARE_WRITE : 0,
                                      NULL, OPEN_EXISTING,
                                      FILE_FLAG_WRITE_THROUGH | FILE_FLAG_NO_BUFFERING, NULL);

        if (context->m_hFile != INVALID_HANDLE_VALUE) {
            try {
                // 扫描文件在磁盘上的位置并擦除相应的簇
                if (wipeClusters(ntc, context, bCompressed)) {
                    // 完成文件句柄操作，关闭句柄
                    CloseHandle(context->m_hFile);
                    context->m_hFile = INVALID_HANDLE_VALUE;

                    if (!bCompressed) {
                        // 如果文件实际上不是压缩文件，返回"非压缩"状态
                        // 调用者应该使用常规擦除方法
                        uResult = WCF_NOTCOMPRESSED;
                    } else {
                        // 文件确实是压缩文件，簇擦除完成后删除文件
                        // 这样可以确保文件的元数据也被清除
                        if (eraserOK(eraserRemoveFile((LPVOID)(LPCTSTR)context->m_strData,
                                (E_UINT16)context->m_strData.GetLength()))) {
                            uResult = WCF_SUCCESS;  // 成功完成压缩文件擦除
                        }
                    }
                } else {
                    // 簇擦除失败，关闭文件句柄
                    CloseHandle(context->m_hFile);
                    context->m_hFile = INVALID_HANDLE_VALUE;
                }
            } catch (CException *e) {
                // 处理异常情况
                handleException(e, context);
                uResult = WCF_FAILURE;

                // 确保文件句柄被正确关闭
                if (context->m_hFile != INVALID_HANDLE_VALUE) {
                    CloseHandle(context->m_hFile);
                    context->m_hFile = INVALID_HANDLE_VALUE;
                }
            }
        }
    } else {
        // 用户没有低级别访问权限（需要管理员权限）
        uResult = WCF_NOACCESS;
    }

    return uResult;
}


// 快速写入测试宏 - 用于测试是否能够在MFT记录中写入数据
#define mftFastWriteTest(x) \
    WriteFile((x)->m_hFile, uTestBuffer, (x)->m_uiFileSize.LowPart, &uTemp, NULL)

/**
 * 擦除MFT记录
 *
 * @param context 擦除上下文
 * @return 成功返回true，失败返回false
 *
 * 功能说明：
 * - 在NTFS文件系统中，小文件可能完全存储在MFT记录中（称为常驻文件）
 * - 当文件被删除后，MFT记录中的数据可能仍然存在
 * - 通过创建大量小文件来填充MFT的空闲记录，从而覆盖残留数据
 * - 只有在磁盘空间为0时才执行（避免浪费磁盘空间）
 */
bool
wipeMFTRecords(CEraserContext *context)
{
    // 检查是否为NTFS文件系统
    if (!isFileSystemNTFS(context->m_piCurrent)) {
        return false;
    }

    // 获取磁盘剩余空间
    E_UINT64 uFreeSpace = 0;
    eraserGetFreeDiskSpace((LPVOID)context->m_piCurrent.m_szDrive,
                           (E_UINT16)lstrlen(context->m_piCurrent.m_szDrive),
                           &uFreeSpace);

    // 只有在没有剩余空间时才执行MFT记录擦除
    // 这样可以确保我们不会浪费用户的磁盘空间
    if (uFreeSpace == 0) {
        const E_UINT16 maxMFTRecordSize = 4096;  // MFT记录的最大大小（4KB）

        TCHAR        szFileName[uShortFileNameLength + 1];  // 短文件名缓冲区
        E_UINT16     uCounter = 1;                          // 文件计数器
        E_UINT32     uTestBuffer[maxMFTRecordSize];         // 测试数据缓冲区
        E_UINT32     uTemp;                                 // 临时变量
        E_UINT64     ulPrevSize = maxMFTRecordSize;         // 上次成功创建的文件大小
        CString      strTemp;                               // 临时字符串
        CStringArray saList;                                // 创建的文件列表
        bool         bCreatedFile;                          // 是否成功创建文件

        // 用随机数据填充测试缓冲区
        // 这些随机数据将被写入MFT记录中，覆盖原有的残留数据
        isaacFill((E_PUINT8)uTestBuffer, maxMFTRecordSize);

        // 设置进度条显示MFT擦除状态，让用户知道正在进行的操作
        eraserDispMFT(context);
        eraserBeginNotify(context);

        context->m_uClusterSpace = 0;  // 重置簇空间计数

        // 主循环：不断创建小文件直到无法再创建为止
        do {
            // 生成随机的短文件名
            createRandomShortFileName(szFileName, uCounter++);
            strTemp.Format(_T("Eraser%s%s"), context->m_piCurrent.m_szDrive, szFileName);

            // 创建新文件
            // CREATE_NEW: 只有文件不存在时才创建
            // FILE_ATTRIBUTE_HIDDEN: 设置为隐藏文件
            // FILE_FLAG_WRITE_THROUGH: 绕过缓存直接写入磁盘
            context->m_hFile = CreateFile((LPCTSTR)strTemp,
                                         GENERIC_WRITE,
                                         (context->m_uTestMode) ?
                                            FILE_SHARE_READ | FILE_SHARE_WRITE : 0,
                                         NULL,
                                         CREATE_NEW,
                                         FILE_ATTRIBUTE_HIDDEN | FILE_FLAG_WRITE_THROUGH,
                                         NULL);

            // 如果无法创建文件，说明MFT已满或出现其他错误
            if (context->m_hFile == INVALID_HANDLE_VALUE) {
                break;
            }

            // 将文件路径添加到列表中，稍后需要删除这些临时文件
            saList.Add(strTemp);

            try {
                context->m_uiFileStart.QuadPart = 0;           // 从文件开始位置写入
                context->m_uiFileSize.QuadPart = ulPrevSize;   // 尝试写入上次成功的大小
                bCreatedFile = false;

                // 尝试写入数据，从大到小逐步减少文件大小
                // 直到找到能够成功写入的最大大小
                while (context->m_uiFileSize.QuadPart) {
                    // 检查是否被用户中断
                    if (eraserInternalTerminated(context)) {
                        bCreatedFile = false;
                        break;
                    }

                    // 首先尝试简单写入测试，这比调用完整的擦除函数快得多
                    // 如果简单写入失败，或者完整擦除失败，则减小文件大小重试
                    if (!mftFastWriteTest(context) || !context->m_lpmMethod->m_pwfFunction(context)) {
                        eraserProgressSetMessage(context, ERASER_MESSAGE_MFT);
                        context->m_uiFileSize.QuadPart--;  // 减小1字节重试
                    } else {
                        // 成功写入，更新进度信息
                        strTemp.Format(ERASER_MESSAGE_MFT_WAIT, uCounter);
                        eraserProgressSetMessage(context, strTemp);
                        eraserUpdateNotify(context);

                        ulPrevSize = context->m_uiFileSize.QuadPart;  // 记录成功的文件大小
                        bCreatedFile = true;
                        break;
                    }

                    // 更新进度百分比
                    eraserSafeAssign(context, context->m_uProgressPercent,
                        (E_UINT8)(((maxMFTRecordSize - context->m_uiFileSize.QuadPart) * 100) / maxMFTRecordSize));
                    setTotalProgress(context);
                    eraserUpdateNotify(context);
                }
            } catch (CException *e) {
                // 处理异常
                handleException(e, context);
                bCreatedFile = false;
            }

            // 重置文件时间戳（安全考虑）并关闭文件句柄
            resetDate(context->m_hFile);
            CloseHandle(context->m_hFile);

        } while (bCreatedFile);  // 只要能成功创建文件就继续循环

        // 设置进度为100%
        eraserSafeAssign(context, context->m_uProgressPercent, 100);
        setTotalProgress(context);

        // 显示"正在删除文件"消息
        eraserProgressSetMessage(context, ERASER_MESSAGE_REMOVING);
        eraserUpdateNotify(context);

        // 删除所有创建的临时文件
        E_INT32 iSize = static_cast<E_INT32>(saList.GetSize());
        for (E_INT32 i = 0; i < iSize; i++) {
            // 更新删除进度
            eraserSafeAssign(context, context->m_uProgressPercent, (E_UINT8)((i * 100) / iSize));
            eraserUpdateNotify(context);

            // 文件名已经是随机的，不需要使用较慢的eraserRemoveFile函数
            // 直接删除即可，因为文件内容已经被随机数据覆盖
            DeleteFile((LPCTSTR)saList[i]);
        }

        // 将创建的文件数量添加到随机数熵池中
        // 这可以增强随机数生成器的随机性
        randomAddEntropy((E_PUINT8)&iSize, sizeof(E_INT32));

        // 最终设置进度为100%
        eraserSafeAssign(context, context->m_uProgressPercent, 100);
        eraserUpdateNotify(context);

        // 清理：将测试缓冲区清零（安全考虑）
        ZeroMemory(uTestBuffer, maxMFTRecordSize);

        return true;  // 成功完成MFT记录擦除
    }

    return false;  // 磁盘还有剩余空间，不需要执行MFT擦除
}

/**
 * 擦除NTFS文件目录项
 *
 * @param context 擦除上下文
 * @return 成功返回true，失败返回false
 *
 * 功能说明：
 * - 通过创建大量文件来扩展MFT（主文件表），从而覆盖已删除文件的目录项
 * - 当文件被删除时，其在MFT中的记录可能仍包含文件名和其他元数据
 * - 通过强制MFT扩展，可以确保这些残留的目录项被新数据覆盖
 * - 这是一种针对NTFS文件系统的高级安全擦除技术
 */
bool
wipeNTFSFileEntries(CEraserContext *context)
{
    // 检查是否为NTFS文件系统
    if (!isFileSystemNTFS(context->m_piCurrent)) {
        return false;
    }

    NTFSContext ntc;        // NTFS操作上下文
    bool bResult = false;   // 操作结果

    // 初始化NTFS上下文并打开卷句柄
    if (initAndOpenVolume(ntc, context->m_strData[0])) {
        IO_STATUS_BLOCK ioStatus;           // I/O状态块
        NTSTATUS status;                    // NT状态码
        NTFS_VOLUME_DATA_BUFFER nvd;        // NTFS卷数据缓冲区

        // 获取MFT大小信息
        // FSCTL_GET_VOLUME_INFORMATION 返回卷的详细信息，包括MFT大小
        status = ntc.NtFsControlFile(ntc.m_hVolume, NULL, NULL, 0, &ioStatus,
                                     FSCTL_GET_VOLUME_INFORMATION,
                                     NULL, 0, &nvd,
                                     sizeof(NTFS_VOLUME_DATA_BUFFER));

        if (status == STATUS_SUCCESS) {
            // 常量定义
            const E_UINT32  uMaxFilesPerFolder = 3000;  // 每个文件夹最大文件数
            const E_UINT32  uMFTPollInterval = 20;      // MFT轮询间隔
            const E_UINT32  uFileNameLength = _MAX_FNAME - 14 - 8 - 1;  // 文件名长度

            // 变量声明
            HANDLE          hFile, hFind;               // 文件句柄和查找句柄
            WIN32_FIND_DATA wfdData;                    // 文件查找数据
            E_UINT32        uSpeed, uTickCount;         // 速度和时间计数
            E_UINT32        i, j;                       // 循环变量
            E_UINT32        uFiles = 0, uFolders = 0;   // 文件和文件夹计数
            E_UINT32        uEstimate;                  // 估计需要创建的文件数
            LARGE_INTEGER   uOriginalMFTSize = nvd.MftValidDataLength;  // 原始MFT大小
            CString         strPath, strFolder;         // 路径字符串
            CStringArray    saFolders;                  // 文件夹数组
            TCHAR           szPrefix[uFileNameLength + 1];  // 文件名前缀

            try {
                // 用'0'字符填充文件名前缀
                // 这样可以确保文件名具有一定的模式，便于后续处理
                for (i = 0; i < uFileNameLength; i++) {
                    szPrefix[i] = '0';
                }
                szPrefix[uFileNameLength] = 0;  // 字符串结束符

                // 估算需要创建的文件数量（至少1个）
                // 基于MFT大小和每个文件记录段的大小来计算
				uEstimate = max(1, (E_UINT32)(nvd.MftValidDataLength.QuadPart / nvd.BytesPerFileRecordSegment));

                // 如果估算值大于已处理的文件数，则减去已处理的数量
                if (uEstimate > context->m_uProgressFiles) {
                    uEstimate -= context->m_uProgressFiles;
                }

                // 这个操作可能需要很长时间，所以启用时间估算
                context->m_uProgressFlags |= eraserDispTime;
                context->m_uProgressStartTime = GetTickCount();

                eraserBeginNotify(context);

                // 主循环：创建文件直到MFT扩展为止
                do {
                    // 每3000个文件创建一个新文件夹
                    // 这样可以避免单个文件夹中文件过多导致的性能问题
                    if (uFiles % uMaxFilesPerFolder == 0) {
                        strFolder.Format(_T("%c:\\%s%04X"), context->m_strData[0],
                                    ERASER_TEMP_DIRECTORY_NTFS_ENTRIES, uFolders++);

                        // 删除可能存在的同名文件夹
                        eraserRemoveFolder((LPVOID)(LPCTSTR)strFolder, (E_UINT16)strFolder.GetLength(),
                                           ERASER_REMOVE_RECURSIVELY);

                        // 创建新目录
                        if (CreateDirectory((LPCTSTR)strFolder, NULL)) {
                            saFolders.Add(strFolder + _T("\\"));  // 添加到文件夹列表
                        } else {
                            eraserAddError(context, IDS_ERROR_TEMPFILE);
                            break;  // 无法创建目录，退出循环
                        }
                    }

                    // 生成文件路径：文件夹路径 + 前缀 + 文件编号
                    strPath.Format(_T("%s\\%s%08X"), strFolder, szPrefix, uFiles);

                    // 创建新文件
                    // CREATE_NEW: 只有文件不存在时才创建
                    hFile = CreateFile((LPCTSTR)strPath, GENERIC_WRITE, 0, NULL, CREATE_NEW, 0, 0);

                    if (hFile != INVALID_HANDLE_VALUE) {
                        uFiles++;           // 增加文件计数
                        CloseHandle(hFile); // 立即关闭文件句柄
                    } else {
                        eraserAddError(context, IDS_ERROR_TEMPFILE);
                        break;  // 无法创建文件，退出循环
                    }

                    // 定期检查MFT大小变化或用户中断
                    if (uFiles % uMFTPollInterval == 0 || eraserInternalTerminated(context)) {
                        // 重新获取卷信息，检查MFT是否已扩展
                        status = ntc.NtFsControlFile(ntc.m_hVolume, NULL, NULL, 0, &ioStatus,
                                                     FSCTL_GET_VOLUME_INFORMATION,
                                                     NULL, 0, &nvd,
                                                     sizeof(NTFS_VOLUME_DATA_BUFFER));

                        if (eraserInternalTerminated(context)) {
                            break;  // 用户中断操作
                        } else {
                            // 计算创建文件的速度并估算剩余时间
                            uTickCount = GetTickCount();
                            if (uTickCount > context->m_uProgressStartTime) {
                                // 计算每秒创建的文件数（乘以1000是为了从毫秒转换为秒）
                                uSpeed = (uFiles * 1000) / (uTickCount - context->m_uProgressStartTime);

                                if (uSpeed > 0) {
                                    // 估算剩余时间（秒）
                                    context->m_uProgressTimeLeft = ((uEstimate - uFiles) / uSpeed);
                                } else {
                                    context->m_uProgressTimeLeft = 0;
                                }
                            }

                            // 更新进度百分比
                            eraserSafeAssign(context, context->m_uProgressPercent,
                                (E_UINT8)min(100, (uFiles * 100) / uEstimate));
                            setTotalProgress(context);
                            eraserUpdateNotify(context);
                        }
                    }
                // 继续循环直到MFT大小增加或操作失败
                // 当MFT大小不再增长时，说明已经成功填充了MFT的空闲空间
				} while (status == STATUS_SUCCESS && nvd.MftValidDataLength.QuadPart <= uOriginalMFTSize.QuadPart);


                // 如果成功增加了MFT大小，说明空闲空间已被填充
				if (nvd.MftValidDataLength.QuadPart > uOriginalMFTSize.QuadPart) {
                    bResult = true;  // 操作成功
                    eraserSafeAssign(context, context->m_uProgressPercent, 100);
                    setTotalProgress(context);

                    // 将创建的文件数量添加到随机数熵池中
                    randomAddEntropy((E_PUINT8)&uFiles, sizeof(E_UINT32));
                }

                // 如果操作没有被用户中断，开始清理临时文件
                if (!eraserInternalTerminated(context)) {
                    // 显示删除文件的进度条 - 这可能需要一些时间
                    eraserProgressSetMessage(context, ERASER_MESSAGE_REMOVING);
                    eraserBeginNotify(context);
                }

                // 删除所有临时文件
                for (i = 0, j = 0; i < uFolders; i++) {
                    strFolder = saFolders[i];
                    // 查找文件夹中的所有文件
                    hFind = FindFirstFile((LPCTSTR)(strFolder + _T("*")), &wfdData);

                    if (hFind != INVALID_HANDLE_VALUE) {
                        do {
                            // 只删除文件，不删除子目录
                            if (!bitSet(wfdData.dwFileAttributes, FILE_ATTRIBUTE_DIRECTORY)) {
                                // eraserRemoveFile太慢，直接使用DeleteFile
                                // 因为这些文件的目的只是占用MFT空间，内容不重要
                                DeleteFile((LPCTSTR)(strFolder + wfdData.cFileName));

                                // 更新删除进度
                                if (!eraserInternalTerminated(context)) {
                                    eraserSafeAssign(context, context->m_uProgressPercent, (E_UINT8)((++j * 100) / uFiles));
                                    eraserUpdateNotify(context);
                                }
                            }
                        }
                        while (FindNextFile(hFind, &wfdData));  // 继续查找下一个文件

                        VERIFY(FindClose(hFind));  // 关闭查找句柄
                    }

                    // 删除文件夹
                    if (eraserError(eraserRemoveFolder((LPVOID)(LPCTSTR)strFolder,
                            (E_UINT16)strFolder.GetLength(), ERASER_REMOVE_RECURSIVELY))) {
                        context->m_saFailed.Add(strFolder);  // 记录删除失败的文件夹
                    }
                }

                // 如果操作没有被中断，设置最终进度为100%
                if (!eraserInternalTerminated(context)) {
                    eraserSafeAssign(context, context->m_uProgressPercent, 100);
                    eraserUpdateNotify(context);
                }
            } catch (CException *e) {
                // 处理异常
                handleException(e, context);
                bResult = false;
            }
        }
    }

    // 如果操作失败，添加错误信息
    if (!bResult) {
        eraserAddError1(context, IDS_ERROR_DIRENTRIES, (LPCTSTR)context->m_strData);
    }

    return bResult;
}

/**
 * 查找文件的备用数据流（Alternate Data Streams, ADS）
 *
 * @param context 擦除上下文
 * @param szFile 要检查的文件路径
 * @param streams 输出参数，存储找到的数据流信息
 * @return 成功返回true，失败返回false
 *
 * 功能说明：
 * - NTFS支持备用数据流，一个文件可以包含多个数据流
 * - 主数据流通常是文件的主要内容，备用数据流可以存储额外信息
 * - 备用数据流经常被恶意软件用来隐藏数据，因此需要特别处理
 * - 这个函数枚举文件的所有数据流，以便进行完整的安全擦除
 */
bool
findAlternateDataStreams(CEraserContext *context, LPCTSTR szFile, DataStreamArray& streams)
{
    // 检查是否为NTFS文件系统（只有NTFS支持备用数据流）
    if (!isFileSystemNTFS(context->m_piCurrent)) {
        return false;
    }

    NTFSContext ntc;
    if (initEntryPoints(ntc)) {
        bool bResult = false;                           // 操作结果
        HANDLE hFile;                                   // 文件句柄
        PFILE_STREAM_INFORMATION psi = 0;               // 流信息指针
        NTSTATUS status = STATUS_INVALID_PARAMETER;     // NT状态码
        WCHAR wszStreamName[MAX_PATH];                  // Unicode流名称缓冲区
        IO_STATUS_BLOCK ioStatus;                       // I/O状态块
        DataStream ads;                                 // 数据流结构

        // 打开文件进行读取
        hFile = CreateFile(szFile,
                           GENERIC_READ,                    // 只需要读权限
                           FILE_SHARE_READ | FILE_SHARE_WRITE,  // 允许共享访问
                           NULL,
                           OPEN_EXISTING,                   // 文件必须存在
                           0, 0);

        if (hFile != INVALID_HANDLE_VALUE) {
            // 使用写缓冲区来存储流信息，缓冲区应该足够大
            // FileStreamInformation 查询类型返回文件的所有数据流信息
            status = ntc.NtQueryInformationFile(hFile, &ioStatus,
                                                (PFILE_STREAM_INFORMATION)context->m_puBuffer,
                                                ERASER_DISK_BUFFER_SIZE,
                                                FileStreamInformation);

            if (NT_SUCCESS(status)) {
                try {
                    psi = (PFILE_STREAM_INFORMATION)context->m_puBuffer;

                    // 遍历所有数据流
                    do {
                        // 复制流名称并添加字符串结束符
                        memcpy(wszStreamName, psi->Name, psi->NameLength);
                        wszStreamName[psi->NameLength / sizeof(WCHAR)] = 0;

                        // 检查是否为备用数据流（不是主数据流"::$DATA"）
                        if (_wcsicmp(wszStreamName, L"::$DATA")) {
                            // 转换Unicode流名称为CString
                            unicodeToCString(wszStreamName, ads.m_strName);
                            // 构造完整的流路径：文件路径 + 流名称
                            ads.m_strName = szFile + ads.m_strName;

                            // 记录流的大小
                            ads.m_uSize = psi->Size.QuadPart;
                            // 添加到流数组中
                            streams.Add(ads);
                        }

                        // 移动到下一个流信息结构
                        if (psi->NextEntry) {
                            psi = (PFILE_STREAM_INFORMATION)((E_PUINT8)psi + psi->NextEntry);
                        } else {
                            psi = 0;  // 没有更多流信息
                        }
                    } while (psi);

                    bResult = true;  // 成功枚举所有流
                } catch (...) {
                    // 捕获所有异常
                    ASSERT(0);  // 调试模式下断言
                    bResult = false;
                }
            }

            CloseHandle(hFile);  // 关闭文件句柄
            return bResult;
        }
    }
    return false;  // 初始化入口点失败或其他错误
}

// ============================================================================
// 文件结束
//
// 本文件实现了NTFS文件系统的高级安全擦除功能：
// 1. wipeCompressedFile() - 擦除压缩文件的实际磁盘簇
// 2. wipeMFTRecords() - 通过创建小文件来覆盖MFT中的残留数据
// 3. wipeNTFSFileEntries() - 通过扩展MFT来覆盖已删除文件的目录项
// 4. findAlternateDataStreams() - 查找文件的备用数据流
//
// 这些功能确保了在NTFS文件系统上的彻底数据擦除，防止数据恢复。
// ============================================================================